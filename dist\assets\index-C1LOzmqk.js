(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))a(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&a(l)}).observe(document,{childList:!0,subtree:!0});function o(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function a(i){if(i.ep)return;i.ep=!0;const r=o(i);fetch(i.href,r)}})();const en=window.location.origin+"/api";async function J(e,t={}){const o=`${en}${e}`,i={...{headers:{"Content-Type":"application/json"}},...t};try{const r=await fetch(o,i);if(!r.ok){const l=await r.json().catch(()=>({error:"Unknown error"}));throw new Error(l.error||`HTTP ${r.status}: ${r.statusText}`)}return await r.json()}catch(r){throw console.error(`API request failed for ${e}:`,r),r}}async function tn(e){try{const t=await J("/quiz-data",{method:"POST",body:JSON.stringify(e)});return console.log("Quiz data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save quiz data to backend:",t),!1}}async function nn(e){try{const t=await J("/leaderboard-data",{method:"POST",body:JSON.stringify(e)});return console.log("Leaderboard data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save leaderboard data to backend:",t),!1}}async function on(e,t){try{const o=await J("/add-category",{method:"POST",body:JSON.stringify({categoryName:e,modes:t})});return console.log("Category added to backend:",o.message),!0}catch(o){return console.error("Failed to add category to backend:",o),!1}}async function an(){try{const e=await J("/quiz-data");return console.log("Quiz data loaded from backend"),e}catch(e){return console.error("Failed to load quiz data from backend:",e),{}}}async function rn(){try{const e=await J("/leaderboard-data");return console.log("Leaderboard data loaded from backend"),e}catch(e){return console.error("Failed to load leaderboard data from backend:",e),{"multiple-choice":{},"image-based":{},"time-limited":{},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}}async function sn(e,t,o){try{const a=await J("/delete-question",{method:"DELETE",body:JSON.stringify({mode:e,category:t,questionIndex:o})});return console.log("Question deleted from backend:",a.message),a}catch(a){throw console.error("Failed to delete question from backend:",a),a}}async function ln(e){try{const t=await J("/delete-category",{method:"DELETE",body:JSON.stringify({categoryName:e})});return console.log("Category deleted from backend:",t.message),t}catch(t){throw console.error("Failed to delete category from backend:",t),t}}async function ut(){try{if(typeof window<"u"&&window.location.hostname.includes("vercel.app"))return console.log("Detected Vercel deployment, skipping backend check"),!1;const e=new AbortController,t=setTimeout(()=>e.abort(),2e3);return await J("/quiz-data",{signal:e.signal}),clearTimeout(t),!0}catch{return console.warn("Backend API not available, falling back to static file loading"),!1}}let g={};async function wt(){try{if(await ut())try{g=await rn(),console.log("Leaderboard data loaded from backend API");return}catch(a){console.warn("Backend API failed for leaderboard, falling back to static files:",a)}const t=await fetch("/data/leaderboardData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Leaderboard response is not JSON, content-type:",o);const a=await t.text();throw console.error("Response text:",a.substring(0,200)),new Error("Invalid JSON response for leaderboard data")}return g=await t.json(),console.log("Leaderboard data loaded from static file"),await cn(),g}catch(e){return console.error("Failed to load leaderboard data:",e),g=qt(),g}}function qt(){return{"image-based":{all:[],landschaft:[],städte_erkennen:[],wahrzeichen:[],geographie_extrem:[],architecture:[],straßenschilder:[]},"time-limited":{all:{"0.1":[],"0.5":[],1:[],2:[],3:[]},landschaft:{"0.1":[],"0.5":[],1:[],2:[],3:[]},städte_erkennen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},wahrzeichen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},geographie_extrem:{"0.1":[],"0.5":[],1:[],2:[],3:[]}},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}function Pe(e,t,o=null){let a=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&o&&(a+=`_${o}s`),a}async function cn(){console.log("Checking for localStorage leaderboard data to migrate...");let e=0;const t=["image-based","time-limited"];for(const o of t)if(g[o])for(const a in g[o])if(o==="time-limited")for(const i in g[o][a]){const r=Pe(o,a,parseFloat(i)),l=It(r);if(l&&l.length>0){const d=g[o][a][i]||[],u=Ue(d,l);g[o][a][i]=u,e+=l.length}}else{const i=Pe(o,a),r=It(i);if(r&&r.length>0){const l=g[o][a]||[],d=Ue(l,r);g[o][a]=d,e+=r.length}}e>0&&(console.log(`Migrated ${e} leaderboard entries from localStorage`),await mt())}function It(e){try{const t=localStorage.getItem(e);return t?JSON.parse(t):[]}catch(t){return console.error("Error reading localStorage leaderboard:",t),[]}}function Ue(e,t){const o=[...e];return t.forEach(a=>{if(!o.some(r=>r.name===a.name&&r.correctAnswers===a.correctAnswers&&r.totalQuestions===a.totalQuestions&&Math.abs(new Date(r.completedAt||r.lastPlayed)-new Date(a.completedAt||a.lastPlayed))<1e3)){const r={name:a.name,correctAnswers:a.correctAnswers,totalQuestions:a.totalQuestions,maxStreak:a.maxStreak||0,completedAt:a.completedAt||a.lastPlayed||new Date().toISOString(),mode:a.mode,category:a.category,timeLimit:a.timeLimit};o.push(r)}}),o.sort((a,i)=>{if(i.correctAnswers!==a.correctAnswers)return i.correctAnswers-a.correctAnswers;const r=a.totalQuestions>0?a.correctAnswers/a.totalQuestions:0,l=i.totalQuestions>0?i.correctAnswers/i.totalQuestions:0;return l!==r?l-r:new Date(i.completedAt||i.lastPlayed)-new Date(a.completedAt||a.lastPlayed)}),o.slice(0,50)}function Ct(e,t="all",o=null){return g[e]?e==="time-limited"?!g[e][t]||!g[e][t][o]?[]:g[e][t][o]||[]:g[e][t]||[]:[]}async function dn(e,t,o,a,i,r,l=null){if(!e||!o||!a||t<0)return!1;const u={name:e||"Anonym",correctAnswers:t,totalQuestions:i,maxStreak:r,completedAt:new Date().toISOString(),mode:o,category:a,timeLimit:l},m=Ct(o,a,l);m.push(u);const b=Ue([],m);return o==="time-limited"?(g[o][a]||(g[o][a]={}),g[o][a][l]=b):(g[o]||(g[o]={}),g[o][a]=b),un(),await mt(),mn(o,a,l,b),typeof window<"u"&&window.updateLeaderboardDisplay&&window.updateLeaderboardDisplay(),!0}function un(){let e=0;Object.keys(g).forEach(t=>{t!=="_metadata"&&(t==="time-limited"?Object.keys(g[t]).forEach(o=>{Object.keys(g[t][o]).forEach(a=>{e+=g[t][o][a].length})}):Object.keys(g[t]).forEach(o=>{e+=g[t][o].length}))}),g._metadata={...g._metadata,lastUpdated:new Date().toISOString(),totalEntries:e}}function mn(e,t,o,a){try{const i=Pe(e,t,o);localStorage.setItem(i,JSON.stringify(a))}catch(i){console.error("Error saving to localStorage:",i)}}async function mt(){try{if(await ut()&&await nn(g))return console.log("Leaderboard data saved to backend"),!0;const t=JSON.stringify(g,null,2);return localStorage.setItem("terraTueftlerLeaderboardPersistent",t),console.log("Leaderboard data saved to localStorage (fallback)"),!0}catch(e){return console.error("Error saving leaderboard data:",e),!1}}async function gn(){try{const e=localStorage.getItem("terraTueftlerLeaderboardPersistent");e?(g=JSON.parse(e),console.log("Loaded leaderboard data from persistent storage")):await wt()}catch(e){console.error("Error loading persistent leaderboard data:",e),await wt()}return g}async function fn(){const e=JSON.parse(JSON.stringify(g));return localStorage.setItem("terraTueftlerLeaderboardBackup",JSON.stringify({timestamp:new Date().toISOString(),data:e})),g=qt(),Object.keys(localStorage).forEach(t=>{t.startsWith("terraTueftlerLeaderboard_")&&localStorage.removeItem(t)}),await mt(),!0}function Tt(){var i;let e=0,t=new Set,o=[],a={};return Object.keys(g).forEach(r=>{r!=="_metadata"&&(a[r]={},r==="time-limited"?Object.keys(g[r]).forEach(l=>{a[r][l]=0,Object.keys(g[r][l]).forEach(d=>{const u=g[r][l][d];e+=u.length,a[r][l]+=u.length,u.forEach(m=>{t.add(m.name),o.push({...m,accuracy:m.totalQuestions>0?m.correctAnswers/m.totalQuestions*100:0})})})}):Object.keys(g[r]).forEach(l=>{const d=g[r][l];e+=d.length,a[r][l]=d.length,d.forEach(u=>{t.add(u.name),o.push({...u,accuracy:u.totalQuestions>0?u.correctAnswers/u.totalQuestions*100:0})})}))}),o.sort((r,l)=>l.correctAnswers!==r.correctAnswers?l.correctAnswers-r.correctAnswers:l.accuracy-r.accuracy),{totalEntries:e,totalPlayers:t.size,topPerformers:o.slice(0,10),categoryStats:a,lastUpdated:(i=g._metadata)==null?void 0:i.lastUpdated}}let c={},je={};async function At(){try{if(await ut())try{c=await an(),console.log("Quiz data loaded from backend API");return}catch(a){console.warn("Backend API failed, falling back to static files:",a)}const t=await fetch("/data/quizData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Response is not JSON, content-type:",o);const a=await t.text();throw console.error("Response text:",a.substring(0,200)),new Error("Invalid JSON response")}c=await t.json(),console.log("Quiz data loaded from static file")}catch(e){console.error("Failed to load quiz data:",e),c={"time-limited":{},"image-based":{},all:[]},console.warn("Using fallback empty quiz data structure")}}async function pn(){try{const e=await fetch("/data/learningData.json");if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=e.headers.get("content-type");if(!t||!t.includes("application/json")){console.warn("Learning data response is not JSON, content-type:",t);const o=await e.text();throw console.error("Response text:",o.substring(0,200)),new Error("Invalid JSON response for learning data")}je=await e.json(),console.log("Learning data loaded successfully")}catch(e){console.error("Failed to load learning data:",e),je={},console.warn("Using fallback empty learning data structure")}}async function yn(){return await Promise.all([At(),pn(),gn()]),console.log("Data initialization completed. Quiz data keys:",Object.keys(c)),console.log("Quiz data structure check:",{hasTimeLimit:!!c["time-limited"],hasImageBased:!!c["image-based"],hasAll:!!c.all,totalKeys:Object.keys(c).length}),{quizData:c,learningData:je}}function de(){return c}function xt(){return c&&Object.keys(c).length>0&&(c["time-limited"]||c["image-based"]||c.questions)}async function Qt(){return console.log("🔄 Refreshing quiz data after admin changes..."),await At(),console.log("✅ Quiz data refreshed successfully"),c}const R={soundEnabled:!0,currentTheme:"theme-standard"};function Dt(e,t){["theme-standard","theme-dark"].forEach(i=>document.body.classList.remove(i));const a=e||"theme-standard";document.body.classList.add(a),R.currentTheme=a,localStorage.setItem("terraTueftlerTheme",a),t&&(t.value=a)}function hn(e,t,o){const a=localStorage.getItem("terraTueftlerTheme")||"theme-standard";Dt(a,e);const i=localStorage.getItem("terraTueftlerSoundEnabled");R.soundEnabled=i!==null?JSON.parse(i):!0,t&&(t.checked=R.soundEnabled),i===null&&localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled)),typeof o=="function"&&o()}function bn(e){R.soundEnabled=e,localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled))}function Re(e){if(!(!R.soundEnabled||!window.AudioContext&&!window.webkitAudioContext))try{const t=new(window.AudioContext||window.webkitAudioContext);let o,a;switch(a=t.createGain(),a.connect(t.destination),a.gain.setValueAtTime(.5,t.currentTime),o=t.createOscillator(),o.connect(a),e){case"correct":o.type="sine",o.frequency.setValueAtTime(523.25,t.currentTime),a.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),o.start(t.currentTime),o.stop(t.currentTime+.3);break;case"incorrect":o.type="square",o.frequency.setValueAtTime(110,t.currentTime),a.gain.exponentialRampToValueAtTime(.001,t.currentTime+.4),o.start(t.currentTime),o.stop(t.currentTime+.4);break;case"quizEnd":const i=[261.63,329.63,391.99,523.25];let r=t.currentTime;i.forEach((l,d)=>{const u=t.createOscillator(),m=t.createGain();u.connect(m),m.connect(t.destination),u.type="triangle",u.frequency.setValueAtTime(l,r+d*.15),m.gain.setValueAtTime(.4,r+d*.15),m.gain.exponentialRampToValueAtTime(.001,r+d*.15+.1),u.start(r+d*.15),u.stop(r+d*.15+.1)});break}setTimeout(()=>{t.state!=="closed"&&t.close().catch(i=>console.warn("Error closing AudioContext:",i))},1e3)}catch(t){console.warn("AudioContext could not be started or used.",t),R.soundEnabled=!1;const o=document.getElementById("sound-toggle");o&&(o.checked=!1)}}console.log("🔧 QUIZ.JS LOADED - Version with question text fix - 2024-fix");const s={currentQuizType:"",currentCategory:"",currentQuestions:[],currentQuestionIndex:0,selectedAnswer:null,userAnswers:[],questionStates:[],score:0,currentStreak:0,maxStreak:0,quizEnded:!1,selectedTimeLimit:1,timerId:null,isTimerRunning:!1,imagePhaseActive:!1};let $e,He,ae,E,L,p,I,h,T,Te,ge,le,v,C,k,P,x,Ke,Ve,_e,ee,te,ie;function gt(){$e=document.getElementById("quiz"),$e&&$e.querySelectorAll(".quiz-option[data-quiz-type]"),document.getElementById("time-limit-options"),document.getElementById("time-limit-select"),document.getElementById("quiz-category"),He=document.querySelector("#quiz-category h2"),ae=document.getElementById("quiz-category-options"),document.getElementById("back-to-quiz-selection"),E=document.getElementById("quiz-game"),L=document.getElementById("question-text"),p=document.getElementById("question-image"),I=document.getElementById("answer-options"),h=document.getElementById("feedback"),T=document.getElementById("timer"),Te=document.getElementById("timer-phase"),ge=document.getElementById("time-left"),le=document.getElementById("prev-question"),v=document.getElementById("submit-answer"),C=document.getElementById("next-question"),k=document.getElementById("quit-quiz"),P=document.getElementById("finish-quiz"),x=document.getElementById("scoreboard"),Ke=document.getElementById("score"),Ve=document.getElementById("final-streak"),_e=document.getElementById("score-message"),ee=document.getElementById("streak-display"),te=document.getElementById("current-streak"),document.getElementById("leaderboard"),document.getElementById("leaderboard-mode-select"),ie=document.getElementById("leaderboard-list"),document.getElementById("clear-leaderboard")}function Ee(){clearInterval(s.timerId),s.currentQuizType="",s.currentCategory="",s.currentQuestions=[],s.currentQuestionIndex=0,s.selectedAnswer=null,s.userAnswers=[],s.questionStates=[],s.score=0,s.currentStreak=0,s.maxStreak=0,s.quizEnded=!1,s.timerId=null,s.isTimerRunning=!1,s.imagePhaseActive=!1,T&&(T.style.display="none"),h&&(h.style.display="none"),x&&(x.style.display="none"),ee&&(ee.style.display="none"),v&&(v.style.display="inline-block"),C&&(C.style.display="none"),le&&(le.style.display="none"),P&&(P.style.display="none"),k&&(k.style.display="none"),L&&(L.textContent="Frage wird geladen..."),p&&(p.style.display="none"),I&&(I.innerHTML="");const e=E?E.querySelector(".question-container"):null,t=E?E.querySelector(".quiz-controls"):null;e&&(e.style.display="block"),t&&(t.style.display="flex")}function Ae(e){if(!ae){console.error("Quiz category options container not found.");return}s.currentQuizType=e,ae.innerHTML="";let t=zt(e);e==="time-limited"&&(t+=` (${s.selectedTimeLimit}s)`),He&&(He.textContent=`Wähle eine Kategorie (${t})`);let o=[];const a=de();if(a.questions)o=Object.keys(a.questions);else{let i=e;if(e==="time-limited"&&(i="image-based"),!a[i]){console.error(`Quiz data source "${i}" not found in quizData.`),ae.innerHTML="<p>Fehler: Quiztyp nicht gefunden.</p>",j("quiz-category");return}o=Object.keys(a[i])}o.length===0?ae.innerHTML="<p>Keine Kategorien für diesen Modus verfügbar.</p>":o.forEach(i=>{const r=document.createElement("div");r.classList.add("quiz-option");const l=i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ");r.textContent=l,r.dataset.category=i,ae.appendChild(r)}),j("quiz-category")}function Je(e,t){var l;Ee(),s.currentQuizType=e,s.currentCategory=t;let o=[];const a=de();if(a.questions&&a.questions[t])o=a.questions[t];else{let d=e;if(e==="time-limited"&&(d="image-based"),!((l=a[d])!=null&&l[t])||a[d][t].length===0){alert("Fehler: Quiz konnte nicht geladen werden. Kategorie oder Typ ungültig."),j("quiz-category");return}o=a[d][t]}if(o.length===0){alert("Fehler: Keine Fragen in dieser Kategorie verfügbar."),j("quiz-category");return}s.currentQuestions=Jt([...o]),s.userAnswers=new Array(s.currentQuestions.length).fill(null),s.questionStates=s.currentQuestions.map(()=>({answered:!1,locked:!1})),E||gt();const i=E==null?void 0:E.querySelector(".question-container"),r=E==null?void 0:E.querySelector(".quiz-controls");i&&(i.style.display="block"),r&&(r.style.display="flex"),L&&(L.style.display="block"),I&&(I.style.display="grid"),x&&(x.style.display="none"),ee&&(ee.style.display="block"),te&&(te.textContent=s.currentStreak),k&&(k.style.display="inline-block"),j("quiz-game"),ft(),e==="time-limited"&&T&&ge&&Te?pt():T&&(T.style.display="none")}function ft(){if(!L||!I||!h||!v||!C||!te||!k){console.error("Required quiz game elements not found for loading question.");return}if(s.quizEnded||s.currentQuestionIndex>=s.currentQuestions.length){xe();return}const e=s.currentQuestions[s.currentQuestionIndex],t=s.questionStates[s.currentQuestionIndex];if(s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],console.log("DEBUG: Current question:",e),console.log("DEBUG: Question field exists:",!!e.question),console.log("DEBUG: Quiz type:",s.currentQuizType),console.log("DEBUG: questionTextElement exists:",!!L),!L&&(console.error("ERROR: questionTextElement is null! Re-caching DOM elements..."),gt(),!L)){console.error("ERROR: questionTextElement still null after re-caching!");return}if(s.currentQuizType==="time-limited")L.style.display="none",console.log("DEBUG: Time-limited quiz - hiding question text");else{if(L.style.display="block",e.question){const i=`Frage ${s.currentQuestionIndex+1}: ${e.question}`;L.textContent=i,console.log("DEBUG: Using original question text:",i)}else{const i=`Frage ${s.currentQuestionIndex+1}: In welchem Land befindet sich das?`;L.textContent=i,console.log("DEBUG: Using fallback question text:",i)}console.log("DEBUG: Final question element text:",L.textContent),console.log("DEBUG: Final question element innerHTML:",L.innerHTML)}I.innerHTML="",h.style.display="none",h.textContent="",te.textContent=s.currentStreak;const o=t.locked;if(o){if(v.style.display="none",C.style.display="inline-block",k.style.display="inline-block",I.style.pointerEvents="none",t.answered){h.style.display="block";const r=s.userAnswers[s.currentQuestionIndex]===e.correctAnswer;h.textContent=r?"✓ Richtig! Diese Antwort ist gesperrt.":"✗ Falsch. Diese Antwort ist gesperrt.",h.className=r?"feedback correct":"feedback incorrect"}}else v.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I.style.pointerEvents="auto";if(p&&e.image){const i=e.image.startsWith("/")?e.image:`/${e.image}`;if(p.src=i,p.style.display="block",e.question?p.alt=`Bild zur Frage: ${e.question}`:p.alt=`Bild zur Frage ${s.currentQuestionIndex+1}`,e.streetViewUrl){p.classList.remove("streetview-locked","streetview-unlocked","streetview-unlocking"),p.classList.add("streetview-available"),p.removeEventListener("click",ke),p.removeEventListener("click",Se);const r=s.questionStates[s.currentQuestionIndex];r&&r.locked?(p.classList.add("streetview-unlocked"),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",ke)):(p.classList.add("streetview-locked"),p.style.cursor="not-allowed",p.title="Beantworte die Frage, um Street View zu entsperren",p.addEventListener("click",Se))}else p.classList.remove("streetview-available","streetview-locked","streetview-unlocked","streetview-unlocking"),p.style.cursor="default",p.title="",p.removeEventListener("click",ke),p.removeEventListener("click",Se)}else p&&(p.style.display="none");Jt([...e.options]).forEach(i=>{const r=document.createElement("div");r.classList.add("answer-option"),r.textContent=i,r.dataset.option=i,o?(r.classList.add("locked"),i===e.correctAnswer?r.classList.add("correct"):i===s.selectedAnswer&&r.classList.add("incorrect"),i===s.selectedAnswer&&r.classList.add("selected")):(i===s.selectedAnswer&&r.classList.add("selected"),r.addEventListener("click",En)),I.appendChild(r)}),Mt()}function En(e){if(!s.questionStates[s.currentQuestionIndex].locked&&v&&v.style.display!=="none"){const o=e.target.dataset.option;s.selectedAnswer=o,s.userAnswers[s.currentQuestionIndex]=o,document.querySelectorAll("#answer-options .answer-option").forEach(a=>{a.classList.remove("selected")}),e.target.classList.add("selected")}}function ke(e){e.preventDefault();const t=s.currentQuestions[s.currentQuestionIndex];t&&t.streetViewUrl&&(window.open(t.streetViewUrl,"_blank","noopener,noreferrer"),f("Street View-Location wird geöffnet...","info"))}function Se(e){e.preventDefault(),f("Beantworte zuerst die Frage, um Street View zu entsperren!","warning")}function vn(){if(!p)return;const e=s.currentQuestions[s.currentQuestionIndex];e&&e.streetViewUrl&&p.classList.contains("streetview-locked")&&(p.classList.remove("streetview-locked"),p.classList.add("streetview-unlocking"),p.removeEventListener("click",Se),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",ke),setTimeout(()=>{p.classList.remove("streetview-unlocking"),p.classList.add("streetview-unlocked")},600))}function Mt(){if(!le||!P||!k||!v||!C)return;le.style.display=s.currentQuestionIndex>0?"inline-block":"none",k&&(k.style.display="inline-block"),v.style.display==="none"?(C.style.display=s.currentQuestionIndex<s.currentQuestions.length-1?"inline-block":"none",P.style.display=s.currentQuestionIndex>=s.currentQuestions.length-1?"inline-block":"none"):(C.style.display="none",P.style.display="none")}function wn(e=!1){if(console.log("checkAnswer called with isTimeout:",e,"current score:",s.score,"current streak:",s.currentStreak),v&&v.style.display==="none"&&!e){console.log("Answer already submitted for this question, ignoring duplicate call");return}if(!v||!h||!I||!C||!P||!te||!k){console.error("Required quiz elements not found for checking answer.");return}if(!e&&!s.selectedAnswer){f("Bitte wähle zuerst eine Antwort aus.","warning");return}clearInterval(s.timerId),s.isTimerRunning=!1,v.style.display="none",I.style.pointerEvents="none";const t=s.currentQuestions[s.currentQuestionIndex],o=!e&&s.selectedAnswer===t.correctAnswer;vn(),o?(s.quizEnded||(console.log("Score before increment:",s.score),s.score++,console.log("Score after increment:",s.score),console.log("Streak before increment:",s.currentStreak),s.currentStreak++,console.log("Streak after increment:",s.currentStreak),s.maxStreak=Math.max(s.maxStreak,s.currentStreak),console.log("Max streak updated to:",s.maxStreak)),h.textContent=`Richtig! ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-correct)",Re("correct")):(e?h.textContent=`Zeit abgelaufen! Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`:h.textContent=`Falsch. Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-incorrect)",Re("incorrect")),te.textContent=s.currentStreak,document.querySelectorAll("#answer-options .answer-option").forEach(a=>{a.classList.remove("selected"),a.dataset.option===t.correctAnswer?a.classList.add("correct"):a.dataset.option===s.selectedAnswer&&!o&&a.classList.add("incorrect")}),h.style.display="block",s.questionStates[s.currentQuestionIndex].answered=!0,Mt()}function In(){!C||!v||!h||!k||(s.questionStates[s.currentQuestionIndex]&&(s.questionStates[s.currentQuestionIndex].locked=!0),s.currentQuestionIndex<s.currentQuestions.length-1?(s.currentQuestionIndex++,s.selectedAnswer=null,h.style.display="none",v.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),ft(),s.currentQuizType==="time-limited"&&pt()):xe())}function kn(){clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,!(!le||!v||!C||!P||!h||!k)&&s.currentQuestionIndex>0&&(s.currentQuestionIndex--,s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],h.style.display="none",v.style.display="inline-block",C.style.display="none",P.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),ft(),s.currentQuizType==="time-limited"&&pt())}async function xe(){if(console.log("finishQuiz called. Current quiz state:",JSON.stringify(s)),clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,s.quizEnded=!0,y.playerName&&y.playerName!=="Anonym"){const o=s.currentQuizType==="time-limited"?s.selectedTimeLimit:null;await dn(y.playerName,s.score,s.currentQuizType,s.currentCategory,s.currentQuestions.length,s.maxStreak,o)}const e=s.currentQuestionIndex<s.currentQuestions.length,t=s.currentQuizType;s.currentCategory,e&&(!x||x.style.display==="none")?(console.log("Finishing mid-quiz, going to category selection."),Ee(),Ae(t||"image-based")):(console.log("Quiz ended naturally or from score screen, showing score."),Sn())}function Sn(){if(!x||!Ke||!Ve||!_e){console.error("Scoreboard elements not found.");return}const e=E==null?void 0:E.querySelector(".question-container"),t=E==null?void 0:E.querySelector(".quiz-controls");e&&(e.style.display="none"),T&&(T.style.display="none"),h&&(h.style.display="none"),t&&(t.style.display="none"),ee&&(ee.style.display="none"),Ke.textContent=`${s.score} / ${s.currentQuestions.length}`,Ve.textContent=s.maxStreak;let o="";const a=s.currentQuestions.length>0?s.score/s.currentQuestions.length:0;if(s.maxStreak>=10?o=`Wow, ${s.maxStreak} in Folge! Spitzenleistung!`:s.maxStreak>=5?o=`Starke Serie von ${s.maxStreak}! Gut gemacht!`:a>=.7?o="Sehr gutes Ergebnis!":a>=.5?o="Gut gemacht!":o="Übung macht den Meister! Schau doch mal im Lernbereich vorbei.",_e.textContent=o,x.style.display="block",Re("quizEnd"),x){let i=x.querySelector(".scoreboard-actions");i||(i=document.createElement("div"),i.className="scoreboard-actions",x.appendChild(i)),i.innerHTML="";const r=document.createElement("button");r.textContent="Erneut spielen (Zufall)",r.className="btn",r.id="play-again-random-btn",r.addEventListener("click",()=>{Je(s.currentQuizType,s.currentCategory)}),i.appendChild(r);const l=document.createElement("button");l.textContent="Fortfahren",l.className="btn",l.id="fortfahren-btn",l.addEventListener("click",()=>{Je(s.currentQuizType,s.currentCategory)}),i.appendChild(l);const d=document.createElement("button");d.textContent="Quiz beenden",d.className="btn",d.id="scoreboard-main-menu-btn",d.addEventListener("click",()=>{Ee(),j("home")}),i.appendChild(d)}}function pt(){if(!T||!ge||!Te)return;s.imagePhaseActive=!0,T.style.display="block",T.style.background="var(--clr-primary)",Te.textContent="Bild sichtbar:";let e=s.selectedTimeLimit;ge.textContent=e.toFixed(1),clearInterval(s.timerId),s.isTimerRunning=!0,p&&(p.style.display="block");const t=e<1?100:1e3,o=e<1?.1:1;s.timerId=setInterval(()=>{e-=o,e<=0?(e=0,clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,p&&(p.style.display="none",f("Bild ausgeblendet - Wähle das Land!","info")),T&&(T.style.display="none")):ge.textContent=e<1?e.toFixed(1):Math.ceil(e)},t)}function Ln(e,t){const o=de();if(o.questions&&o.questions[t])return o.questions[t].length;{let a=e;return e==="time-limited"&&(a="image-based"),!o[a]||!o[a][t]?0:o[a][t].length}}function Bn(e,t="all",o=null){if(!ie||!e){ie||console.error("Leaderboard list element not found.");return}const a=Ct(e,t,o),i=Ln(e,t);if(ie.innerHTML="",a.length===0){const r=zt(e),l=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),d=o?` (${o}s)`:"";ie.innerHTML=`<li style="text-align: center; color: var(--clr-text-light);">Noch keine Einträge für "${r} - ${l}${d}" vorhanden. Spiel ein Quiz!</li>`;return}a.forEach((r,l)=>{const d=document.createElement("li");d.classList.add("leaderboard-item");const u=i>0&&r.correctAnswers>=i;u&&d.classList.add("perfect-score");const m=document.createElement("span");m.classList.add("leaderboard-rank"),m.textContent=`${l+1}.`;const b=document.createElement("span");b.classList.add("leaderboard-name");const z=u?"⭐ ":"";b.textContent=z+r.name;const A=document.createElement("span");A.classList.add("leaderboard-score");const N=r.totalQuestions>0?Math.round(r.correctAnswers/r.totalQuestions*100):0;A.textContent=`${r.correctAnswers}/${r.totalQuestions} (${N}%)`;const O=document.createElement("span");O.classList.add("leaderboard-streak"),r.maxStreak!==void 0&&(O.textContent=`Serie: ${r.maxStreak}`);const Q=document.createElement("span");if(Q.classList.add("leaderboard-time"),r.completedAt){const D=new Date(r.completedAt),w=D.toLocaleDateString("de-DE")+" "+D.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});Q.textContent=w}else if(r.lastPlayed){const D=new Date(r.lastPlayed),w=D.toLocaleDateString("de-DE")+" "+D.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});Q.textContent=w}d.appendChild(m),d.appendChild(b),d.appendChild(A),r.maxStreak!==void 0&&d.appendChild(O),Q.textContent&&d.appendChild(Q),ie.appendChild(d)})}function zt(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}const S={isInitialized:!1,dragDropInitialized:!1,currentStats:{totalCategories:0,totalQuestions:0,totalModes:2}};let n={},Y=!1;function $t(){n={adminSection:document.getElementById("admin"),addCategoryBtn:document.getElementById("add-category-btn"),addQuestionBtn:document.getElementById("add-question-btn"),manageContentBtn:document.getElementById("manage-content-btn"),exportDataBtn:document.getElementById("export-data-btn"),importDataBtn:document.getElementById("import-data-btn"),totalCategoriesSpan:document.getElementById("total-categories"),totalQuestionsSpan:document.getElementById("total-questions"),totalModesSpan:document.getElementById("total-modes"),totalLeaderboardEntriesSpan:document.getElementById("total-leaderboard-entries"),totalPlayersSpan:document.getElementById("total-players"),clearLeaderboardBtn:document.getElementById("clear-leaderboard-btn"),leaderboardStatsBtn:document.getElementById("leaderboard-stats-btn"),leaderboardStatsModal:document.getElementById("leaderboard-stats-modal"),closeStatsModal:document.getElementById("close-stats-modal"),closeStatsBtn:document.getElementById("close-stats"),statsTotalEntries:document.getElementById("stats-total-entries"),statsTotalPlayers:document.getElementById("stats-total-players"),statsLastUpdated:document.getElementById("stats-last-updated"),topPerformersList:document.getElementById("top-performers-list"),categoryStats:document.getElementById("category-stats"),addCategoryModal:document.getElementById("add-category-modal"),closeCategoryModal:document.getElementById("close-category-modal"),categoryNameInput:document.getElementById("category-name"),modeImageBased:document.getElementById("mode-image-based"),modeTimeLimited:document.getElementById("mode-time-limited"),categoryDescriptionInput:document.getElementById("category-description"),cancelCategoryBtn:document.getElementById("cancel-category"),confirmCategoryBtn:document.getElementById("confirm-category"),addQuestionModal:document.getElementById("add-question-modal"),closeQuestionModal:document.getElementById("close-question-modal"),questionCategorySelect:document.getElementById("question-category"),questionTextInput:document.getElementById("admin-question-text"),questionImageInput:document.getElementById("question-image"),questionImageFileInput:document.getElementById("question-image-file"),imagePreview:document.getElementById("image-preview"),previewImg:document.getElementById("preview-img"),imageFilename:document.getElementById("image-filename"),imageSize:document.getElementById("image-size"),option1Input:document.getElementById("option-1"),option2Input:document.getElementById("option-2"),option3Input:document.getElementById("option-3"),option4Input:document.getElementById("option-4"),correctAnswerSelect:document.getElementById("correct-answer"),questionExplanationInput:document.getElementById("question-explanation"),questionStreetViewUrlInput:document.getElementById("question-streetview-url"),questionPreview:document.getElementById("question-preview"),previewQuestionBtn:document.getElementById("preview-question"),cancelQuestionBtn:document.getElementById("cancel-question"),confirmQuestionBtn:document.getElementById("confirm-question"),contentManagementModal:document.getElementById("content-management-modal"),closeContentModal:document.getElementById("close-content-modal"),categoriesTab:document.getElementById("categories-tab"),questionsTab:document.getElementById("questions-tab"),categoriesContent:document.getElementById("categories-content"),questionsContent:document.getElementById("questions-content"),categoriesList:document.getElementById("categories-list"),questionsList:document.getElementById("questions-list"),filterMode:document.getElementById("filter-mode"),filterCategory:document.getElementById("filter-category"),closeContentBtn:document.getElementById("close-content"),deleteConfirmationModal:document.getElementById("delete-confirmation-modal"),closeDeleteModal:document.getElementById("close-delete-modal"),deleteModalTitle:document.getElementById("delete-modal-title"),deleteModalMessage:document.getElementById("delete-modal-message"),deleteModalDetails:document.getElementById("delete-modal-details"),cancelDeleteBtn:document.getElementById("cancel-delete"),confirmDeleteBtn:document.getElementById("confirm-delete"),deleteBtnText:document.getElementById("delete-btn-text"),deleteLoading:document.getElementById("delete-loading")}}function qn(){S.isInitialized||(console.log("🔧 Initializing admin interface..."),$t(),console.log("=== ADMIN ELEMENTS DEBUG ==="),Object.keys(n).forEach(e=>{console.log(`${e}:`,n[e]?"✅ Found":"❌ Not found")}),Cn(),Nt(),Ot(),Ut(),jt(),Vt(),_n()&&(console.log("Data migration needed - migrating to unified structure..."),Vn().then(e=>{e?(console.log("Data migration completed successfully"),f("Datenstruktur wurde automatisch aktualisiert und optimiert!","success")):(console.error("Data migration failed"),f("Warnung: Datenstruktur-Update fehlgeschlagen","warning")),_()})),_(),S.isInitialized=!0,console.log("Admin interface initialized"))}function Cn(){n.addCategoryBtn&&n.addCategoryBtn.addEventListener("click",Tn),n.addQuestionBtn&&n.addQuestionBtn.addEventListener("click",An),n.manageContentBtn&&n.manageContentBtn.addEventListener("click",Jn),n.exportDataBtn&&n.exportDataBtn.addEventListener("click",Un),n.importDataBtn&&n.importDataBtn.addEventListener("click",jn),n.clearLeaderboardBtn&&n.clearLeaderboardBtn.addEventListener("click",Hn),n.leaderboardStatsBtn&&n.leaderboardStatsBtn.addEventListener("click",Kn),Ut(),Nt(),Ot(),jt(),Vt()}function Nt(){n.closeCategoryModal&&n.closeCategoryModal.addEventListener("click",Le),n.cancelCategoryBtn&&n.cancelCategoryBtn.addEventListener("click",Le),n.confirmCategoryBtn&&n.confirmCategoryBtn.addEventListener("click",Nn),n.addCategoryModal&&n.addCategoryModal.addEventListener("click",e=>{e.target===n.addCategoryModal&&Le()})}function Ot(){n.closeQuestionModal&&n.closeQuestionModal.addEventListener("click",Be),n.cancelQuestionBtn&&n.cancelQuestionBtn.addEventListener("click",Be),n.confirmQuestionBtn&&n.confirmQuestionBtn.addEventListener("click",Fn),n.previewQuestionBtn&&n.previewQuestionBtn.addEventListener("click",Pn),n.questionImageInput&&n.questionImageInput.addEventListener("input",Dn),n.questionImageFileInput&&n.questionImageFileInput.addEventListener("change",Qn),[n.option1Input,n.option2Input,n.option3Input,n.option4Input].forEach(t=>{t&&t.addEventListener("input",$n)}),n.addQuestionModal&&n.addQuestionModal.addEventListener("click",t=>{t.target===n.addQuestionModal&&Be()})}function _(){let e=0,t=0;Object.keys(c).forEach(a=>{const i=Object.keys(c[a]);e+=i.length,i.forEach(r=>{t+=c[a][r].length})});const o=new Set;Object.keys(c).forEach(a=>{Object.keys(c[a]).forEach(i=>{o.add(i)})}),S.currentStats.totalCategories=o.size,S.currentStats.totalQuestions=t;try{const a=Tt();S.currentStats.totalLeaderboardEntries=a.totalEntries,S.currentStats.totalPlayers=a.totalPlayers}catch(a){console.error("Error getting leaderboard stats:",a),S.currentStats.totalLeaderboardEntries=0,S.currentStats.totalPlayers=0}n.totalCategoriesSpan&&(n.totalCategoriesSpan.textContent=S.currentStats.totalCategories),n.totalQuestionsSpan&&(n.totalQuestionsSpan.textContent=S.currentStats.totalQuestions),n.totalModesSpan&&(n.totalModesSpan.textContent=S.currentStats.totalModes),n.totalLeaderboardEntriesSpan&&(n.totalLeaderboardEntriesSpan.textContent=S.currentStats.totalLeaderboardEntries),n.totalPlayersSpan&&(n.totalPlayersSpan.textContent=S.currentStats.totalPlayers)}function Tn(){n.addCategoryModal&&(Ft(),n.addCategoryModal.style.display="flex",n.categoryNameInput&&n.categoryNameInput.focus())}function Le(){n.addCategoryModal&&(n.addCategoryModal.style.display="none",Ft())}function Ft(){n.categoryNameInput&&(n.categoryNameInput.value=""),n.categoryDescriptionInput&&(n.categoryDescriptionInput.value=""),n.modeImageBased&&(n.modeImageBased.checked=!1),n.modeTimeLimited&&(n.modeTimeLimited.checked=!1)}function An(){n.addQuestionModal&&(Pt(),xn(),n.addQuestionModal.style.display="flex",n.questionCategorySelect&&n.questionCategorySelect.focus(),S.dragDropInitialized||setTimeout(()=>{zn(),S.dragDropInitialized=!0},200))}function Be(){n.addQuestionModal&&(n.addQuestionModal.style.display="none",Pt())}function Pt(){Y=!1,editState={isEditing:!1,mode:null,category:null,questionIndex:null,originalQuestion:null};const e=document.querySelector("#add-question-modal .modal-header h3");e&&(e.textContent="Neue Frage hinzufügen");const t=n.confirmQuestionBtn;t&&(t.textContent="Frage hinzufügen",t.disabled=!1),n.questionCategorySelect&&(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',n.questionCategorySelect.disabled=!1),n.questionTextInput&&(n.questionTextInput.value=""),n.questionImageInput&&(n.questionImageInput.value=""),n.questionImageFileInput&&(n.questionImageFileInput.value=""),re(),n.option1Input&&(n.option1Input.value=""),n.option2Input&&(n.option2Input.value=""),n.option3Input&&(n.option3Input.value=""),n.option4Input&&(n.option4Input.value=""),n.correctAnswerSelect&&(n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>'),n.questionExplanationInput&&(n.questionExplanationInput.value=""),n.questionStreetViewUrlInput&&(n.questionStreetViewUrlInput.value=""),n.questionPreview&&(n.questionPreview.style.display="none")}function xn(){if(!n.questionCategorySelect)return;n.questionCategorySelect.innerHTML="";let e=[];if(c.questions)e=Object.keys(c.questions).filter(t=>t!=="all");else{const t=new Set;["image-based","time-limited"].forEach(o=>{c[o]&&Object.keys(c[o]).forEach(a=>{a!=="all"&&t.add(a)})}),e=Array.from(t)}console.log("=== CATEGORY LOADING DEBUG ==="),console.log("Available categories:",e),console.log("Using unified structure:",!!c.questions),e.length===0?(n.questionCategorySelect.innerHTML='<option value="">Keine Kategorien verfügbar</option>',n.questionCategorySelect.disabled=!0):(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',e.sort().forEach(t=>{const o=document.createElement("option");o.value=t,o.textContent=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),n.questionCategorySelect.appendChild(o)}),n.questionCategorySelect.disabled=!1)}function Qn(e){if(Y){console.log("File upload already in progress, ignoring duplicate event");return}Y=!0;try{const t=e.target.files[0];if(!t){re();return}if(!t.type.startsWith("image/")){f("Bitte wähle eine gültige Bilddatei aus.","warning"),e.target.value="",re();return}if(t.size>10*1024*1024){f("Bilddatei ist zu groß. Maximum: 10MB","warning"),e.target.value="",re();return}n.questionImageInput&&(n.questionImageInput.value="");const o=new FileReader;o.onload=a=>{yt(a.target.result,t.name,t.size)},o.readAsDataURL(t)}finally{setTimeout(()=>{Y=!1},100)}}function Dn(){if(!n.questionImageInput||!n.imagePreview||!n.previewImg)return;const e=n.questionImageInput.value.trim();e&&X(e)?(n.questionImageFileInput&&(n.questionImageFileInput.value=""),n.previewImg.src=e,n.previewImg.onload=()=>{yt(e,"Externe URL",null)},n.previewImg.onerror=()=>{re(),f("Bild konnte nicht geladen werden. Überprüfe die URL.","warning")}):re()}function yt(e,t,o){!n.imagePreview||!n.previewImg||(n.previewImg.src=e,n.imagePreview.style.display="block",n.imageFilename&&(n.imageFilename.textContent=t),n.imageSize&&o?n.imageSize.textContent=Mn(o):n.imageSize&&(n.imageSize.textContent=""))}function re(){n.imagePreview&&(n.imagePreview.style.display="none")}function Mn(e){if(e===0)return"0 Bytes";const t=1024,o=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,a)).toFixed(2))+" "+o[a]}function zn(){const e=document.querySelector(".file-upload-label"),t=n.addQuestionModal;if(console.log("🔧 Setting up drag and drop..."),console.log("File upload label found:",!!e),console.log("Add question modal found:",!!t),!e||!t){console.warn("❌ Drag and drop setup failed - missing elements");return}console.log("✅ Drag and drop setup successful"),["dragenter","dragover","dragleave","drop"].forEach(d=>{document.addEventListener(d,o,!1),t.addEventListener(d,o,!1)}),["dragenter","dragover"].forEach(d=>{e.addEventListener(d,a,!1)}),["dragleave","drop"].forEach(d=>{e.addEventListener(d,i,!1)}),e.addEventListener("drop",r,!1),t.addEventListener("drop",l,!1);function o(d){d.preventDefault(),d.stopPropagation()}function a(d){console.log("🎯 Drag enter/over detected"),e.classList.add("drag-over")}function i(d){console.log("🎯 Drag leave/drop detected"),e.classList.remove("drag-over")}function r(d){if(console.log("📁 Drop event triggered"),Y){console.log("File upload already in progress, ignoring drop event");return}const m=d.dataTransfer.files;if(console.log("Files dropped:",m.length),m.length>0){const b=m[0];if(!b.type.startsWith("image/")){f("Bitte ziehe eine gültige Bilddatei hierher.","warning");return}if(b.size>10*1024*1024){f("Bilddatei ist zu groß. Maximum: 10MB","warning");return}if(Y=!0,n.questionImageFileInput){const z=new DataTransfer;z.items.add(b),n.questionImageFileInput.files=z.files,n.questionImageInput&&(n.questionImageInput.value="");const A=new FileReader;A.onload=N=>{yt(N.target.result,b.name,b.size),setTimeout(()=>{Y=!1},100)},A.readAsDataURL(b)}f("Bild erfolgreich hochgeladen!","success")}}function l(d){d.target.closest(".file-upload-label")||(console.log("📁 Modal drop event triggered"),r(d))}}function $n(){var o,a,i,r;if(!n.correctAnswerSelect)return;const e=[(o=n.option1Input)==null?void 0:o.value.trim(),(a=n.option2Input)==null?void 0:a.value.trim(),(i=n.option3Input)==null?void 0:i.value.trim(),(r=n.option4Input)==null?void 0:r.value.trim()].filter(l=>l&&l.length>0),t=n.correctAnswerSelect.value;n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>',e.forEach(l=>{const d=document.createElement("option");d.value=l,d.textContent=l,n.correctAnswerSelect.appendChild(d)}),t&&e.includes(t)&&(n.correctAnswerSelect.value=t)}function X(e){try{return new URL(e),!0}catch{return!1}}async function Nn(){var i,r,l,d;const e=(i=n.categoryNameInput)==null?void 0:i.value.trim();(r=n.categoryDescriptionInput)==null||r.value.trim();const t=[];if((l=n.modeImageBased)!=null&&l.checked&&t.push("image-based"),(d=n.modeTimeLimited)!=null&&d.checked&&t.push("time-limited"),!e){f("Bitte gib einen Kategorie-Namen ein.","warning");return}if(!On(e)){f("Kategorie-Name darf nur Kleinbuchstaben, Zahlen und Unterstriche enthalten.","warning");return}if(t.length===0){f("Bitte wähle mindestens einen Quiz-Modus aus.","warning");return}const o=[];if(t.forEach(u=>{let m=u;u==="time-limited"&&(m="image-based"),c[m]&&c[m][e]&&o.push(u)}),o.length>0){const u=o.join(", ");f(`Kategorie "${e}" existiert bereits in: ${u}`,"warning");return}t.forEach(u=>{let m=u;u==="time-limited"&&(m="image-based"),c[m]||(c[m]={}),c[m][e]=[]});try{await on(e,t)?console.log("Category saved to backend successfully"):console.warn("Failed to save category to backend, using localStorage backup")}catch(u){console.error("Error saving category to backend:",u)}ve(),_(),Le();const a=t.join(", ");f(`Kategorie "${e}" erfolgreich zu ${a} hinzugefügt!`,"success")}function On(e){return/^[a-z0-9_]+$/.test(e)}async function Fn(e){var b,z,A,N,O,Q,D,w,Me,ze,Et;if(e&&(e.preventDefault(),e.stopPropagation()),n.confirmQuestionBtn){if(n.confirmQuestionBtn.disabled){console.log("Question submission already in progress, ignoring duplicate request");return}n.confirmQuestionBtn.disabled=!0,n.confirmQuestionBtn.textContent="Wird hinzugefügt..."}const t=(b=n.questionCategorySelect)==null?void 0:b.value,o=(z=n.questionTextInput)==null?void 0:z.value.trim(),a=(A=n.questionImageInput)==null?void 0:A.value.trim(),i=(N=n.questionImageFileInput)==null?void 0:N.files[0],r=(O=n.questionExplanationInput)==null?void 0:O.value.trim(),l=(Q=n.questionStreetViewUrlInput)==null?void 0:Q.value.trim(),d=[(D=n.option1Input)==null?void 0:D.value.trim(),(w=n.option2Input)==null?void 0:w.value.trim(),(Me=n.option3Input)==null?void 0:Me.value.trim(),(ze=n.option4Input)==null?void 0:ze.value.trim()].filter(B=>B&&B.length>0),u=(Et=n.correctAnswerSelect)==null?void 0:Et.value,m=()=>{n.confirmQuestionBtn&&(n.confirmQuestionBtn.disabled=!1,n.confirmQuestionBtn.textContent="Frage hinzufügen")};try{if(console.log("=== QUESTION SUBMISSION DEBUG ==="),console.log("Category:",t),console.log("Question Text:",o),console.log("Image URL:",a),console.log("Image File:",i),console.log("Options:",d),console.log("Correct Answer:",u),console.log("Explanation:",r),console.log("Street View URL:",l),!t){f("Bitte wähle eine Kategorie aus.","warning"),m();return}if(t.toLowerCase()==="all"){f('Die "All"-Kategorie wird automatisch verwaltet. Bitte wähle eine spezifische Kategorie.',"warning"),m();return}if(!i&&(!a||!X(a))){f("Ein Bild (Upload oder URL) ist für alle Fragen erforderlich.","warning"),m();return}if(d.length<2){f("Bitte gib mindestens 2 Antwortmöglichkeiten ein.","warning"),m();return}if(!u){f("Bitte wähle die richtige Antwort aus.","warning"),m();return}if(!d.includes(u)){f("Die richtige Antwort muss eine der Antwortmöglichkeiten sein.","warning"),m();return}const B={options:d,correctAnswer:u};o&&(B.question=o),r&&(B.explanation=r),l&&X(l)&&(B.streetViewUrl=l),a&&X(a)&&(B.image=a);const G=new FormData;G.append("mode","both"),G.append("category",t),G.append("questionData",JSON.stringify(B));const vt=Date.now()+"_"+Math.random().toString(36).substring(2,11);G.append("requestId",vt),i&&(!a||!X(a))&&(G.append("image",i),console.log("Adding image file to form data:",i.name,"size:",i.size)),console.log("Submitting question with request ID:",vt),console.log("Form data contents:");for(let[ue,me]of G.entries())console.log(`  ${ue}:`,me);const W=await fetch("/api/add-question",{method:"POST",body:G});if(console.log("Response status:",W.status),console.log("Response ok:",W.ok),!W.ok){const ue=await W.text();console.error("Server error response:",ue);let me;try{me=JSON.parse(ue)}catch{me={error:ue||"Unknown server error"}}throw new Error(me.error||`HTTP ${W.status}: ${W.statusText}`)}const Ie=await W.json();console.log("Question added successfully:",Ie),Ie.imagePath&&(B.image=Ie.imagePath),c.questions||(c.questions={}),c.questions[t]||(c.questions[t]=[]),c.questions[t].push(B);let oe=mode;mode==="time-limited"&&(oe="image-based"),c[oe]||(c[oe]={}),c[oe][t]||(c[oe][t]=[]),c[oe][t].push(B);const Yt=Ie.imagePath?" (Bild automatisch organisiert)":"";f(`Frage erfolgreich zu "${t}" (beide Quiz-Modi) hinzugefügt!${Yt}`,"success"),ve(),_(),De(),Be()}catch(B){console.error("Error adding question:",B),f(`Fehler beim Hinzufügen der Frage: ${B.message}`,"error")}finally{m()}}function Pn(){var u,m,b,z,A,N,O,Q,D;if(!n.questionPreview)return;const e=(u=n.questionTextInput)==null?void 0:u.value.trim(),t=(m=n.questionImageInput)==null?void 0:m.value.trim(),o=(b=n.questionImageFileInput)==null?void 0:b.files[0],a=(z=n.questionStreetViewUrlInput)==null?void 0:z.value.trim(),i=[(A=n.option1Input)==null?void 0:A.value.trim(),(N=n.option2Input)==null?void 0:N.value.trim(),(O=n.option3Input)==null?void 0:O.value.trim(),(Q=n.option4Input)==null?void 0:Q.value.trim()].filter(w=>w&&w.length>0),r=(D=n.correctAnswerSelect)==null?void 0:D.value;let l="";if(e&&(l+=`<div style="margin-bottom: 1rem; font-weight: 600;">${e}</div>`),t&&X(t))l+=`<img src="${t}" alt="Frage-Bild" class="preview-image">`;else if(o){const w=n.previewImg;w&&w.src&&(l+=`<img src="${w.src}" alt="Frage-Bild" class="preview-image">`)}i.length>0&&(l+='<div class="preview-options">',i.forEach(w=>{l+=`<div class="${w===r?"preview-option correct":"preview-option"}">${w}</div>`}),l+="</div>"),a&&X(a)&&(l+=`<div style="margin-top: 1rem; padding: 0.5rem; background: #e8f4fd; border-radius: 4px; font-size: 0.9rem;">
            🌍 Street View verfügbar: Spieler können nach der Antwort die Location besuchen
        </div>`);const d=n.questionPreview.querySelector(".preview-content");d&&(d.innerHTML=l),n.questionPreview.style.display="block"}function Un(){try{const e=JSON.stringify(c,null,2),t=new Blob([e],{type:"application/json"}),o=document.createElement("a");o.href=URL.createObjectURL(t),o.download=`terraTueftler-quizData-${new Date().toISOString().split("T")[0]}.json`,o.click(),f("Quiz-Daten erfolgreich exportiert!","success")}catch(e){console.error("Export error:",e),f("Fehler beim Exportieren der Daten.","error")}}function jn(){const e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=t=>{const o=t.target.files[0];if(!o)return;const a=new FileReader;a.onload=i=>{try{const r=JSON.parse(i.target.result);if(!Rn(r)){f("Ungültige Datenstruktur. Bitte überprüfe die JSON-Datei.","error");return}confirm("Möchtest du die aktuellen Quiz-Daten durch die importierten Daten ersetzen? Diese Aktion kann nicht rückgängig gemacht werden.")&&(ve(),Object.keys(r).forEach(l=>{c[l]=r[l]}),_(),f("Quiz-Daten erfolgreich importiert!","success"))}catch(r){console.error("Import error:",r),f("Fehler beim Importieren der Daten. Überprüfe das JSON-Format.","error")}},a.readAsText(o)},e.click()}function Rn(e){if(typeof e!="object"||e===null)return!1;const t=["image-based","time-limited"];for(const o in e)if(t.includes(o)){if(typeof e[o]!="object"||e[o]===null)return!1;for(const a in e[o]){if(!Array.isArray(e[o][a]))return!1;for(const i of e[o][a])if(!i.options||!Array.isArray(i.options)||i.options.length<2||!i.correctAnswer||!i.options.includes(i.correctAnswer))return!1}}return!0}function ve(){try{const t={timestamp:new Date().toISOString(),data:c};localStorage.setItem("terraTueftlerQuizDataBackup",JSON.stringify(t)),console.log("Quiz data backup saved to localStorage")}catch(e){console.error("Error saving backup:",e)}}function Ut(){n.closeStatsModal&&n.closeStatsModal.addEventListener("click",Ne),n.closeStatsBtn&&n.closeStatsBtn.addEventListener("click",Ne),n.leaderboardStatsModal&&n.leaderboardStatsModal.addEventListener("click",e=>{e.target===n.leaderboardStatsModal&&Ne()})}async function Hn(){if(confirm("Möchtest du ALLE Ranglisten-Daten wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."))try{await fn(),_(),f("Alle Ranglisten-Daten wurden erfolgreich gelöscht!","success")}catch(e){console.error("Clear error:",e),f("Fehler beim Löschen der Ranglisten-Daten.","error")}}async function Kn(){if(n.leaderboardStatsModal)try{const e=Tt();if(n.statsTotalEntries&&(n.statsTotalEntries.textContent=e.totalEntries),n.statsTotalPlayers&&(n.statsTotalPlayers.textContent=e.totalPlayers),n.statsLastUpdated){const t=e.lastUpdated?new Date(e.lastUpdated).toLocaleString("de-DE"):"Nie";n.statsLastUpdated.textContent=t}n.topPerformersList&&(n.topPerformersList.innerHTML="",e.topPerformers.length===0?n.topPerformersList.innerHTML="<p>Keine Leistungen vorhanden</p>":e.topPerformers.forEach((t,o)=>{const a=document.createElement("div");a.className="performer-item",a.innerHTML=`
                        <span class="rank">${o+1}.</span>
                        <span class="name">${t.name}</span>
                        <span class="score">${t.correctAnswers}/${t.totalQuestions}</span>
                        <span class="accuracy">${t.accuracy.toFixed(1)}%</span>
                        <span class="mode">${t.mode}</span>
                        <span class="category">${t.category}</span>
                    `,n.topPerformersList.appendChild(a)})),n.categoryStats&&(n.categoryStats.innerHTML="",Object.keys(e.categoryStats).forEach(t=>{const o=document.createElement("div");o.className="mode-stats",o.innerHTML=`<h5>${we(t)}</h5>`,Object.keys(e.categoryStats[t]).forEach(a=>{const i=e.categoryStats[t][a],r=document.createElement("div");r.className="category-stat",r.innerHTML=`
                        <span class="category-name">${a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," ")}</span>
                        <span class="category-count">${i} Einträge</span>
                    `,o.appendChild(r)}),n.categoryStats.appendChild(o)})),n.leaderboardStatsModal.style.display="flex"}catch(e){console.error("Error showing leaderboard stats:",e),f("Fehler beim Laden der Statistiken.","error")}}function Ne(){n.leaderboardStatsModal&&(n.leaderboardStatsModal.style.display="none")}function we(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}async function Vn(){if(console.log("Starting data migration to unified structure..."),!c)return console.error("Quiz data not available for migration"),!1;c.questions||(c.questions={});const e=new Map;let t=0,o=0;["image-based","time-limited"].forEach(i=>{c[i]&&Object.keys(c[i]).forEach(r=>{r!=="all"&&Array.isArray(c[i][r])&&c[i][r].forEach(l=>{const d=`${l.image||"no-image"}_${l.correctAnswer}_${r}`;e.has(d)?o++:(c.questions[r]||(c.questions[r]=[]),c.questions[r].push(l),e.set(d,!0),t++)})})}),console.log(`Migration completed: ${t} questions migrated, ${o} duplicates removed`);try{return await tn(c),ve(),!0}catch(i){return console.error("Error saving migrated data:",i),!1}}function _n(){if(!c)return!1;if(!c.questions||Object.keys(c.questions).length===0){const e=["image-based","time-limited"];for(const t of e)if(c[t]&&Object.keys(c[t]).length>0)return!0}return!1}function Jn(){n.contentManagementModal&&(n.contentManagementModal.style.display="flex",Rt(),Ht(),De(),ht())}function Oe(){n.contentManagementModal&&(n.contentManagementModal.style.display="none")}function jt(){n.closeContentModal&&n.closeContentModal.addEventListener("click",Oe),n.closeContentBtn&&n.closeContentBtn.addEventListener("click",Oe),n.categoriesTab&&n.categoriesTab.addEventListener("click",Rt),n.questionsTab&&n.questionsTab.addEventListener("click",Gn),n.filterMode&&n.filterMode.addEventListener("change",kt),n.filterCategory&&n.filterCategory.addEventListener("change",kt),n.contentManagementModal&&n.contentManagementModal.addEventListener("click",e=>{e.target===n.contentManagementModal&&Oe()})}function Rt(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.categoriesTab.classList.add("active"),n.questionsTab.classList.remove("active"),n.categoriesContent.classList.add("active"),n.questionsContent.classList.remove("active"))}function Gn(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.questionsTab.classList.add("active"),n.categoriesTab.classList.remove("active"),n.questionsContent.classList.add("active"),n.categoriesContent.classList.remove("active"),ht())}function Ht(){if(!n.categoriesList)return;const e=new Set,t={};if(Object.keys(c).forEach(o=>{Object.keys(c[o]).forEach(a=>{e.add(a),t[a]||(t[a]={modes:[],totalQuestions:0}),t[a].modes.push(o),t[a].totalQuestions+=c[o][a].length})}),n.categoriesList.innerHTML="",e.size===0){n.categoriesList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">📁</div>
                <p>Keine Kategorien vorhanden</p>
            </div>
        `;return}Array.from(e).sort().forEach(o=>{const a=t[o],i=document.createElement("div");i.className="content-item";const r=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," "),l=a.modes.map(d=>we(d)).join(", ");i.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">${r}</div>
                <div class="content-item-meta">
                    <span>Modi: ${l}</span>
                    <span>${a.totalQuestions} Fragen</span>
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteCategory('${o}')">
                    🗑️ Löschen
                </button>
            </div>
        `,n.categoriesList.appendChild(i)})}function De(){if(!n.questionsList)return;const e=[];Object.keys(c).forEach(t=>{Object.keys(c[t]).forEach(o=>{c[t][o].forEach((a,i)=>{e.push({mode:t,category:o,index:i,question:a,displayName:o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," ")})})})}),Kt(e)}function Kt(e){if(n.questionsList){if(n.questionsList.innerHTML="",e.length===0){n.questionsList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">❓</div>
                <p>Keine Fragen vorhanden</p>
            </div>
        `;return}e.forEach(t=>{const o=document.createElement("div");o.className="content-item";const a=t.question.question||"Bildbasierte Frage",i=!!t.question.image,r=i?`<img src="${t.question.image}" class="question-preview-mini" alt="Vorschau">`:"";o.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">
                    ${r}
                    <span class="question-text-preview">${a}</span>
                </div>
                <div class="content-item-meta">
                    <span>Kategorie: ${t.displayName}</span>
                    <span>Modus: ${we(t.mode)}</span>
                    <span>Antwort: ${t.question.correctAnswer}</span>
                    ${i?"<span>📷 Mit Bild</span>":""}
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteQuestion('${t.mode}', '${t.category}', ${t.index})">
                    🗑️ Löschen
                </button>
            </div>
        `,n.questionsList.appendChild(o)})}}function ht(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value;n.filterMode.innerHTML=`
        <option value="">Alle Modi</option>
        <option value="image-based">Bildbasiert</option>
        <option value="time-limited">Zeitbegrenzt</option>
    `,n.filterMode.value=e;const t=new Set;Object.keys(c).forEach(a=>{Object.keys(c[a]).forEach(i=>{t.add(i)})});const o=n.filterCategory.value;n.filterCategory.innerHTML='<option value="">Alle Kategorien</option>',Array.from(t).sort().forEach(a=>{const i=a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," "),r=document.createElement("option");r.value=a,r.textContent=i,n.filterCategory.appendChild(r)}),n.filterCategory.value=o}function kt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value,t=n.filterCategory.value,o=[];Object.keys(c).forEach(a=>{a!=="questions"&&(e&&a!==e||c[a]&&typeof c[a]=="object"&&Object.keys(c[a]).forEach(i=>{t&&i!==t||Array.isArray(c[a][i])&&c[a][i].forEach((r,l)=>{o.push({mode:a,category:i,index:l,question:r,displayName:i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ")})})}))}),Kt(o)}let M={type:null,data:null};function Vt(){n.closeDeleteModal&&n.closeDeleteModal.addEventListener("click",qe),n.cancelDeleteBtn&&n.cancelDeleteBtn.addEventListener("click",qe),n.confirmDeleteBtn&&n.confirmDeleteBtn.addEventListener("click",Zn),n.deleteConfirmationModal&&n.deleteConfirmationModal.addEventListener("click",e=>{e.target===n.deleteConfirmationModal&&qe()})}function qe(){n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="none",M={type:null,data:null})}window.confirmDeleteCategory=function(e){const t=Wn(e);if(M={type:"category",data:{categoryName:e}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Kategorie löschen"),n.deleteModalMessage&&(n.deleteModalMessage.textContent=`Möchtest du die Kategorie "${e}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`),n.deleteModalDetails){const o=e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ");n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Kategorie:</strong> ${o}</li>
                <li><strong>Betroffene Modi:</strong> ${t.modes.map(a=>we(a)).join(", ")}</li>
                <li><strong>Anzahl Fragen:</strong> ${t.totalQuestions}</li>
                <li><strong>Bilder:</strong> ${t.imageCount} Dateien werden gelöscht</li>
                <li><strong>Ordner:</strong> Kategorie-Ordner wird entfernt (falls leer)</li>
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Kategorie löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};window.confirmDeleteQuestion=function(e,t,o){const a=c[e]&&c[e][t]&&c[e][t][o];if(!a){f("Frage nicht gefunden.","error");return}M={type:"question",data:{mode:e,category:t,questionIndex:o}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Frage löschen");const i=a.question||"Bildbasierte Frage";if(n.deleteModalMessage&&(n.deleteModalMessage.textContent="Möchtest du diese Frage wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."),n.deleteModalDetails){const r=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),l=!!a.image;n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Frage:</strong> ${i}</li>
                <li><strong>Kategorie:</strong> ${r}</li>
                <li><strong>Modus:</strong> ${we(e)}</li>
                <li><strong>Richtige Antwort:</strong> ${a.correctAnswer}</li>
                ${l?`<li><strong>Bild:</strong> ${a.image} wird gelöscht</li>`:""}
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Frage löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};function Wn(e){const t={modes:[],totalQuestions:0,imageCount:0};return Object.keys(c).forEach(o=>{if(c[o][e]){t.modes.push(o);const a=c[o][e];t.totalQuestions+=a.length,a.forEach(i=>{i.image&&i.image.startsWith("assets/images/")&&t.imageCount++})}}),t}async function Zn(){if(!(!M.type||!M.data)){n.deleteBtnText&&(n.deleteBtnText.style.display="none"),n.deleteLoading&&(n.deleteLoading.style.display="inline-block"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!0);try{M.type==="category"?await Xn(M.data.categoryName):M.type==="question"&&await Yn(M.data.mode,M.data.category,M.data.questionIndex)}catch(e){console.error("Deletion error:",e),f(`Fehler beim Löschen: ${e.message}`,"error")}finally{n.deleteBtnText&&(n.deleteBtnText.style.display="inline"),n.deleteLoading&&(n.deleteLoading.style.display="none"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!1),qe()}}}async function Xn(e){try{const t=await ln(e);Object.keys(c).forEach(a=>{c[a][e]&&delete c[a][e]}),c.questions&&c.questions[e]&&(delete c.questions[e],console.log(`Removed category "${e}" from unified structure`)),ve(),await Qt(),_(),Ht(),De(),ht();const o=t.deletedQuestions>0?` (${t.deletedQuestions} Fragen, ${t.deletedImages.length} Bilder gelöscht)`:"";f(`Kategorie "${e}" erfolgreich gelöscht!${o}`,"success")}catch(t){throw console.error("Error deleting category:",t),new Error(`Kategorie konnte nicht gelöscht werden: ${t.message}`)}}async function Yn(e,t,o){try{console.log("=== DELETE QUESTION DEBUG ==="),console.log("Mode:",e),console.log("Category:",t),console.log("Question Index:",o);let a=e;e==="time-limited"&&(a="image-based");const i=c[a]&&c[a][t]&&c[a][t][o];if(console.log("Question to delete:",i),!i)throw new Error("Frage nicht gefunden");const r=await sn(e,t,o);console.log("Backend deletion result:",r),await Qt(),console.log("Quiz data refreshed from server"),_(),De();const l=r.deletedImagePath?" (Bild gelöscht)":"";f(`Frage erfolgreich gelöscht!${l}`,"success")}catch(a){throw console.error("Error deleting question:",a),new Error(`Frage konnte nicht gelöscht werden: ${a.message}`)}}const y={currentSection:"home",playerName:"Anonym"};let se,F,bt,_t,H,Ge,We,he,Z,Qe,fe,ne,q,$,U,be,Ce,Ze,Xe,K,V,Ye,et,tt,nt,ot,at,it,rt,st,lt,ct;function eo(){se=document.getElementById("nav-links"),F=document.getElementById("burger"),bt=document.querySelectorAll("main > section"),_t=document.querySelectorAll(".nav-links a[data-target]"),document.querySelectorAll(".btn[data-target]"),document.getElementById("player-name-area"),H=document.getElementById("player-name"),Ge=document.getElementById("save-player-name"),We=document.getElementById("current-player-name"),document.getElementById("learn"),document.getElementById("learn-overview"),he=document.getElementById("learn-category-select"),Z=document.getElementById("learn-content-grid"),Qe=document.getElementById("sound-toggle"),fe=document.getElementById("theme-select"),ne=document.getElementById("time-limit-select"),q=document.getElementById("time-limit-options"),$=document.getElementById("leaderboard-mode-select"),U=document.getElementById("leaderboard-category-select"),be=document.getElementById("leaderboard-time-select"),Ce=document.getElementById("time-limit-filter"),Ze=document.getElementById("current-player-display"),Xe=document.getElementById("change-player-btn"),K=document.getElementById("player-name-modal"),V=document.getElementById("new-player-name"),Ye=document.getElementById("close-player-modal"),et=document.getElementById("cancel-player-change"),tt=document.getElementById("confirm-player-change"),nt=document.querySelectorAll("#quiz .quiz-option[data-quiz-type]"),ct=document.getElementById("quiz-category-options"),ot=document.getElementById("back-to-quiz-selection"),at=document.getElementById("submit-answer"),it=document.getElementById("next-question"),rt=document.getElementById("prev-question"),st=document.getElementById("quit-quiz"),lt=document.getElementById("finish-quiz")}function Jt(e){for(let t=e.length-1;t>0;t--){const o=Math.floor(Math.random()*(t+1));[e[t],e[o]]=[e[o],e[t]]}return e}function f(e,t="info"){const o=document.createElement("div");switch(o.textContent=e,o.style.position="fixed",o.style.bottom="20px",o.style.left="50%",o.style.transform="translateX(-50%)",o.style.padding="10px 20px",o.style.borderRadius="var(--radius)",o.style.color="#fff",o.style.zIndex="2000",o.style.boxShadow="var(--shadow-lg)",o.style.textAlign="center",o.style.opacity="0",o.style.transition="opacity 0.5s ease",t){case"warning":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"error":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"info":default:o.style.backgroundColor="var(--clr-secondary)";break}document.body.appendChild(o),requestAnimationFrame(()=>{o.style.opacity="1"}),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{o.parentNode&&o.remove()},500)},3e3)}function j(e){if(document.getElementById(e)||(console.error(`Section with ID "${e}" not found. Defaulting to home.`),e="home"),y.currentSection=e,bt.forEach(o=>{const a=o.id===e;o.style.display=a?"block":"none"}),_t.forEach(o=>{o.classList.toggle("active",o.dataset.target===e)}),se&&F&&se.classList.contains("show")&&(se.classList.remove("show"),F.classList.remove("change"),F.setAttribute("aria-expanded","false")),e!=="quiz-game"&&e!=="quiz-category"&&Ee(),e!=="quiz"&&q){q.style.display="none";const o=document.getElementById("start-time-limited-quiz"),a=document.getElementById("time-limit-instruction");o&&(o.style.display="none"),a&&(a.style.display="none")}e==="learn"&&(Xt("all"),he&&(he.value="all")),e==="leaderboard"&&to(),e==="admin"&&qn(),e==="quiz"&&dt(),window.scrollTo(0,0)}function to(){!$||!U||(Gt(),Wt(),pe())}function Gt(){if(!$||!U)return;const e=$.value;U.innerHTML="";const t=de();let o=e;if(e==="time-limited"&&(o="image-based"),!t[o])return;const a=Object.keys(t[o]);let i;i=a,i.forEach(r=>{const l=document.createElement("option");l.value=r,l.textContent=r.charAt(0).toUpperCase()+r.slice(1).replace(/_/g," "),U.appendChild(l)})}function Wt(){if(!Ce||!$)return;$.value==="time-limited"?Ce.style.display="block":Ce.style.display="none"}function pe(){if(!$||!U)return;const e=$.value,t=U.value,o=e==="time-limited"&&be?parseFloat(be.value):null;Bn(e,t,o)}window.updateLeaderboardDisplay=pe;function dt(){Ze&&(Ze.textContent=`Aktueller Spieler: ${y.playerName}`)}function no(){K&&V&&(V.value=y.playerName==="Anonym"?"":y.playerName,K.style.display="flex",V.focus())}function ye(){K&&(K.style.display="none")}function St(){if(!V)return;const e=V.value.trim();e&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",e),ce(),dt(),ye(),f(`Spieler geändert zu: ${e}`,"info")):e.length>20?f("Name darf maximal 20 Zeichen haben.","warning"):(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName","Anonym"),ce(),dt(),ye(),f('Spieler auf "Anonym" zurückgesetzt.',"info"))}function ce(){H&&(H.value=y.playerName),We&&(We.textContent=`Aktueller Spieler: ${y.playerName}`)}function Lt(){if(!H)return;const e=H.value.trim();e&&e.length>0&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce(),f("Name gespeichert!","info")):(f("Bitte gib einen gültigen Namen ein (1-20 Zeichen).","warning"),H.value=y.playerName)}function Zt(){if(!y.playerName||y.playerName==="Anonym"){const e=prompt("Bitte gib deinen Spielernamen für die Rangliste ein (max. 20 Zeichen):",y.playerName!=="Anonym"?y.playerName:"");return e&&e.trim()!==""&&e.trim().length<=20?(y.playerName=e.trim(),localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce(),!0):e!==null?(alert("Ungültiger Name. Bitte 1-20 Zeichen verwenden."),Zt()):(localStorage.getItem("terraTueftlerPlayerName")||(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce()),!1)}return!0}function oo(){if(s.currentQuizType="time-limited",q){q.style.display="block";let e=document.getElementById("time-limit-instruction");if(e)e.style.display="block";else{e=document.createElement("p"),e.id="time-limit-instruction",e.textContent="Wähle dein gewünschtes Zeitlimit pro Frage:",e.style.marginBottom="0.5rem",e.style.fontWeight="600";const o=q.querySelector("select");o?q.insertBefore(e,o):q.appendChild(e)}let t=document.getElementById("start-time-limited-quiz");t?t.style.display="block":(t=document.createElement("button"),t.id="start-time-limited-quiz",t.className="btn",t.textContent="Quiz starten",t.style.marginTop="1rem",t.style.display="block",t.style.width="100%",t.addEventListener("click",()=>{ne&&(s.selectedTimeLimit=parseFloat(ne.value)||1),q.style.display="none",Ae("time-limited")}),q.appendChild(t))}else console.error("Time limit options div not found"),Ae("time-limited")}function Xt(e="all"){if(!Z){console.warn("Learning content grid not found");return}const t=de();if(!xt()){console.error("Quiz data not available for learning content"),Z.innerHTML='<p class="no-content">Lerndaten konnten nicht geladen werden.</p>';return}let o=[];try{if(t.questions)e==="all"?Object.keys(t.questions).forEach(i=>{if(i!=="all"&&t.questions[i]){const r=t.questions[i];Array.isArray(r)&&r.forEach(l=>{l.image&&o.push({...l,category:i,sourceType:"unified"})})}}):t.questions[e]&&Array.isArray(t.questions[e])&&t.questions[e].forEach(i=>{i.image&&o.push({...i,category:e,sourceType:"unified"})});else{const i=["image-based"],r=new Set;e==="all"?i.forEach(l=>{t[l]&&Object.keys(t[l]).forEach(d=>{if(d!=="all"&&t[l][d]){const u=t[l][d];Array.isArray(u)&&u.forEach(m=>{m.image&&!r.has(m.image)&&(r.add(m.image),o.push({...m,category:d,sourceType:l}))})}})}):i.forEach(l=>{t[l]&&t[l][e]&&Array.isArray(t[l][e])&&t[l][e].forEach(d=>{d.image&&!r.has(d.image)&&(r.add(d.image),o.push({...d,category:e,sourceType:l}))})})}}catch(i){console.error("Error processing learning content:",i),Z.innerHTML='<p class="no-content">Fehler beim Laden der Lerninhalte.</p>';return}if(Z.innerHTML="",o.length===0){Z.innerHTML='<p class="no-content">Keine Lerninhalte für diese Kategorie verfügbar.</p>';return}console.log(`Learning content loaded: ${o.length} items from category "${e}"`);const a=o.reduce((i,r)=>(i[r.sourceType]=(i[r.sourceType]||0)+1,i),{});console.log("Content by source:",a),o.sort((i,r)=>i.category!==r.category?i.category.localeCompare(r.category):i.correctAnswer.localeCompare(r.correctAnswer)),o.forEach(i=>{const r=ao(i);Z.appendChild(r)})}function ao(e){const t=document.createElement("div");t.className="learn-reference-item";let o=e.image||"https://placehold.co/400x200/bdc3c7/2c3e50?text=Kein+Bild";e.image&&!e.image.startsWith("http")&&!e.image.startsWith("/")&&(o=`/${e.image}`);const a=e.category.charAt(0).toUpperCase()+e.category.slice(1).replace(/_/g," "),i=e.sourceType?`<span class="learn-reference-source" title="Quelle: ${e.sourceType}">${io(e.sourceType)}</span>`:"";return t.innerHTML=`
        <img src="${o}" alt="Lerninhalt" class="learn-reference-image" loading="lazy">
        <div class="learn-reference-content">
            <div class="learn-reference-answer">${e.correctAnswer}</div>
            <div class="learn-reference-explanation">${e.explanation||"Keine zusätzlichen Informationen verfügbar."}</div>
            <div class="learn-reference-meta">
                <div class="learn-reference-category">${a}</div>
                ${i}
            </div>
        </div>
    `,t}function io(e){switch(e){case"image-based":return"Bildrätsel";case"time-limited":return"Zeitlimit";default:return e}}let Bt=!1;function ro(){if(Bt){console.log("Event listeners already set up, skipping duplicate setup");return}console.log("Setting up event listeners"),Bt=!0,F&&se&&F.addEventListener("click",()=>{const e=F.getAttribute("aria-expanded")==="true";F.setAttribute("aria-expanded",String(!e)),se.classList.toggle("show"),F.classList.toggle("change")}),document.querySelectorAll(".nav-links a[data-target], .btn[data-target]").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const o=e.dataset.target;o&&j(o)})}),Ge&&H&&(Ge.addEventListener("click",Lt),H.addEventListener("keypress",e=>{e.key==="Enter"&&Lt()})),nt&&nt.forEach(e=>{e.addEventListener("click",()=>{const t=e.dataset.quizType;if(t){const o=de();if(!xt()){console.error("Quiz data validation failed:",{quizData:o,keys:Object.keys(o),hasTimeLimit:!!o["time-limited"],hasImageBased:!!o["image-based"],hasQuestions:!!o.questions}),f("Quiz-Daten konnten nicht geladen werden. Bitte laden Sie die Seite neu.","error");return}if(Zt())if(t==="time-limited")oo();else{if(q){q.style.display="none";const a=document.getElementById("start-time-limited-quiz"),i=document.getElementById("time-limit-instruction");a&&(a.style.display="none"),i&&(i.style.display="none")}Ae(t)}}})}),ne&&ne.addEventListener("change",e=>{s.selectedTimeLimit=parseFloat(e.target.value)||1}),ct&&ct.addEventListener("click",e=>{const t=e.target.closest(".quiz-option[data-category]");if(t){const o=t.dataset.category;s.currentQuizType&&o&&Je(s.currentQuizType,o)}}),ot&&ot.addEventListener("click",()=>{if(Ee(),q){q.style.display="none";const e=document.getElementById("start-time-limited-quiz"),t=document.getElementById("time-limit-instruction");e&&(e.style.display="none"),t&&(t.style.display="none")}j("quiz")}),at&&(console.log("Adding event listener to submit button"),at.addEventListener("click",()=>{console.log("Submit button clicked, calling checkAnswer"),wn(!1)})),it&&it.addEventListener("click",In),rt&&rt.addEventListener("click",kn),lt&&lt.addEventListener("click",xe),st&&st.addEventListener("click",xe),he&&he.addEventListener("change",e=>{const t=e.target.value;Xt(t)}),$&&$.addEventListener("change",()=>{Gt(),Wt(),pe()}),U&&U.addEventListener("change",()=>{pe()}),be&&be.addEventListener("change",()=>{pe()}),Xe&&Xe.addEventListener("click",no),Ye&&Ye.addEventListener("click",ye),et&&et.addEventListener("click",ye),tt&&tt.addEventListener("click",St),V&&V.addEventListener("keypress",e=>{e.key==="Enter"&&St()}),K&&K.addEventListener("click",e=>{e.target===K&&ye()}),Qe&&Qe.addEventListener("change",e=>{bn(e.target.checked)}),fe&&fe.addEventListener("change",e=>{Dt(e.target.value,fe)})}let Fe=!1;document.addEventListener("DOMContentLoaded",async()=>{if(Fe){console.warn("⚠️ TerraTüftler already initialized, skipping duplicate initialization");return}console.log("🚀 TerraTüftler DOM fully loaded and parsed - starting initialization..."),Fe=!0;try{await yn(),console.log("✅ Data initialized successfully"),eo(),gt(),$t(),y.playerName=localStorage.getItem("terraTueftlerPlayerName")||"Anonym",ce(),hn(fe,Qe,ro);const e=window.location.hash.substring(1)||"home",t=Array.from(bt).map(o=>o.id);j(t.includes(e)?e:"home"),ne&&(ne.value=String(s.selectedTimeLimit)),console.log("🎉 TerraTüftler App Initialized successfully!")}catch(e){console.error("❌ Failed to initialize TerraTüftler:",e),f("Fehler beim Laden der Anwendung. Bitte laden Sie die Seite neu.","error"),Fe=!1}});
