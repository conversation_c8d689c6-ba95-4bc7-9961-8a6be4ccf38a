(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))i(a);new MutationObserver(a=>{for(const r of a)if(r.type==="childList")for(const c of r.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&i(c)}).observe(document,{childList:!0,subtree:!0});function o(a){const r={};return a.integrity&&(r.integrity=a.integrity),a.referrerPolicy&&(r.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?r.credentials="include":a.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(a){if(a.ep)return;a.ep=!0;const r=o(a);fetch(a.href,r)}})();const sn=window.location.origin+"/api";async function W(e,t={}){const o=`${sn}${e}`,a={...{headers:{"Content-Type":"application/json"}},...t};try{const r=await fetch(o,a);if(!r.ok){const c=await r.json().catch(()=>({error:"Unknown error"}));throw new Error(c.error||`HTTP ${r.status}: ${r.statusText}`)}return await r.json()}catch(r){throw console.error(`API request failed for ${e}:`,r),r}}async function ln(e){try{const t=await W("/quiz-data",{method:"POST",body:JSON.stringify(e)});return console.log("Quiz data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save quiz data to backend:",t),!1}}async function cn(e){try{const t=await W("/leaderboard-data",{method:"POST",body:JSON.stringify(e)});return console.log("Leaderboard data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save leaderboard data to backend:",t),!1}}async function dn(e,t){try{const o=await W("/add-category",{method:"POST",body:JSON.stringify({categoryName:e,modes:t})});return console.log("Category added to backend:",o.message),!0}catch(o){return console.error("Failed to add category to backend:",o),!1}}async function un(){try{const e=await W("/quiz-data");return console.log("Quiz data loaded from backend"),e}catch(e){return console.error("Failed to load quiz data from backend:",e),{}}}async function mn(){try{const e=await W("/leaderboard-data");return console.log("Leaderboard data loaded from backend"),e}catch(e){return console.error("Failed to load leaderboard data from backend:",e),{"multiple-choice":{},"image-based":{},"time-limited":{},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}}async function gn(e,t,o){try{const i=await W("/delete-question",{method:"DELETE",body:JSON.stringify({mode:e,category:t,questionIndex:o})});return console.log("Question deleted from backend:",i.message),i}catch(i){throw console.error("Failed to delete question from backend:",i),i}}async function fn(e){try{const t=await W("/delete-category",{method:"DELETE",body:JSON.stringify({categoryName:e})});return console.log("Category deleted from backend:",t.message),t}catch(t){throw console.error("Failed to delete category from backend:",t),t}}async function mt(){try{if(typeof window<"u"&&window.location.hostname.includes("vercel.app"))return console.log("Detected Vercel deployment, skipping backend check"),!1;const e=new AbortController,t=setTimeout(()=>e.abort(),2e3);return await W("/quiz-data",{signal:e.signal}),clearTimeout(t),!0}catch{return console.warn("Backend API not available, falling back to static file loading"),!1}}let u={};async function Bt(){try{if(await mt())try{u=await mn(),console.log("Leaderboard data loaded from backend API");return}catch(i){console.warn("Backend API failed for leaderboard, falling back to static files:",i)}const t=await fetch("./data/leaderboardData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Leaderboard response is not JSON, content-type:",o);const i=await t.text();throw console.error("Response text:",i.substring(0,200)),new Error("Invalid JSON response for leaderboard data")}return u=await t.json(),console.log("Leaderboard data loaded from static file"),await pn(),u}catch(e){return console.error("Failed to load leaderboard data:",e),u=xt(),u}}function xt(){return{"image-based":{all:[],landschaft:[],städte_erkennen:[],wahrzeichen:[],geographie_extrem:[],architecture:[],straßenschilder:[]},"time-limited":{all:{"0.1":[],"0.5":[],1:[],2:[],3:[]},landschaft:{"0.1":[],"0.5":[],1:[],2:[],3:[]},städte_erkennen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},wahrzeichen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},geographie_extrem:{"0.1":[],"0.5":[],1:[],2:[],3:[]}},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}function Oe(e,t,o=null){let i=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&o&&(i+=`_${o}s`),i}async function pn(){console.log("Checking for localStorage leaderboard data to migrate...");let e=0;const t=["image-based","time-limited"];for(const o of t)if(u[o])for(const i in u[o])if(o==="time-limited")for(const a in u[o][i]){const r=Oe(o,i,parseFloat(a)),c=Lt(r);if(c&&c.length>0){const d=u[o][i][a]||[],m=Pe(d,c);u[o][i][a]=m,e+=c.length}}else{const a=Oe(o,i),r=Lt(a);if(r&&r.length>0){const c=u[o][i]||[],d=Pe(c,r);u[o][i]=d,e+=r.length}}e>0&&(console.log(`Migrated ${e} leaderboard entries from localStorage`),await Qe())}function Lt(e){try{const t=localStorage.getItem(e);return t?JSON.parse(t):[]}catch(t){return console.error("Error reading localStorage leaderboard:",t),[]}}function Pe(e,t){const o=[...e];return t.forEach(i=>{if(!o.some(r=>r.name===i.name&&r.correctAnswers===i.correctAnswers&&r.totalQuestions===i.totalQuestions&&Math.abs(new Date(r.completedAt||r.lastPlayed)-new Date(i.completedAt||i.lastPlayed))<1e3)){const r={name:i.name,correctAnswers:i.correctAnswers,totalQuestions:i.totalQuestions,maxStreak:i.maxStreak||0,completedAt:i.completedAt||i.lastPlayed||new Date().toISOString(),mode:i.mode,category:i.category,timeLimit:i.timeLimit};o.push(r)}}),o.sort((i,a)=>{if(a.correctAnswers!==i.correctAnswers)return a.correctAnswers-i.correctAnswers;const r=i.totalQuestions>0?i.correctAnswers/i.totalQuestions:0,c=a.totalQuestions>0?a.correctAnswers/a.totalQuestions:0;return c!==r?c-r:new Date(a.completedAt||a.lastPlayed)-new Date(i.completedAt||i.lastPlayed)}),o.slice(0,50)}function Qt(e,t="all",o=null){return u[e]?e==="time-limited"?!u[e][t]||!u[e][t][o]?[]:u[e][t][o]||[]:u[e][t]||[]:[]}async function yn(e,t,o,i,a,r,c=null){if(!e||!o||!i||t<0)return!1;const m={name:e||"Anonym",correctAnswers:t,totalQuestions:a,maxStreak:r,completedAt:new Date().toISOString(),mode:o,category:i,timeLimit:c},f=Qt(o,i,c);f.push(m);const E=Pe([],f);return o==="time-limited"?(u[o][i]||(u[o][i]={}),u[o][i][c]=E):(u[o]||(u[o]={}),u[o][i]=E),Mt(),await Qe(),hn(o,i,c,E),!0}function Mt(){let e=0;Object.keys(u).forEach(t=>{t!=="_metadata"&&(t==="time-limited"?Object.keys(u[t]).forEach(o=>{Object.keys(u[t][o]).forEach(i=>{e+=u[t][o][i].length})}):Object.keys(u[t]).forEach(o=>{e+=u[t][o].length}))}),u._metadata={...u._metadata,lastUpdated:new Date().toISOString(),totalEntries:e}}function hn(e,t,o,i){try{const a=Oe(e,t,o);localStorage.setItem(a,JSON.stringify(i))}catch(a){console.error("Error saving to localStorage:",a)}}async function Qe(){try{if(await mt()&&await cn(u))return console.log("Leaderboard data saved to backend"),!0;const t=JSON.stringify(u,null,2);return localStorage.setItem("terraTueftlerLeaderboardPersistent",t),console.log("Leaderboard data saved to localStorage (fallback)"),!0}catch(e){return console.error("Error saving leaderboard data:",e),!1}}async function En(){try{const e=localStorage.getItem("terraTueftlerLeaderboardPersistent");e?(u=JSON.parse(e),console.log("Loaded leaderboard data from persistent storage")):await Bt()}catch(e){console.error("Error loading persistent leaderboard data:",e),await Bt()}return u}async function bn(){const e=JSON.parse(JSON.stringify(u));return localStorage.setItem("terraTueftlerLeaderboardBackup",JSON.stringify({timestamp:new Date().toISOString(),data:e})),u=xt(),Object.keys(localStorage).forEach(t=>{t.startsWith("terraTueftlerLeaderboard_")&&localStorage.removeItem(t)}),await Qe(),!0}function Dt(){var a;let e=0,t=new Set,o=[],i={};return Object.keys(u).forEach(r=>{r!=="_metadata"&&(i[r]={},r==="time-limited"?Object.keys(u[r]).forEach(c=>{i[r][c]=0,Object.keys(u[r][c]).forEach(d=>{const m=u[r][c][d];e+=m.length,i[r][c]+=m.length,m.forEach(f=>{t.add(f.name),o.push({...f,accuracy:f.totalQuestions>0?f.correctAnswers/f.totalQuestions*100:0})})})}):Object.keys(u[r]).forEach(c=>{const d=u[r][c];e+=d.length,i[r][c]=d.length,d.forEach(m=>{t.add(m.name),o.push({...m,accuracy:m.totalQuestions>0?m.correctAnswers/m.totalQuestions*100:0})})}))}),o.sort((r,c)=>c.correctAnswers!==r.correctAnswers?c.correctAnswers-r.correctAnswers:c.accuracy-r.accuracy),{totalEntries:e,totalPlayers:t.size,topPerformers:o.slice(0,10),categoryStats:i,lastUpdated:(a=u._metadata)==null?void 0:a.lastUpdated}}async function vn(e,t="all",o=null){if(u[e]){if(e==="time-limited"&&o!==null){const i=String(o);u[e][t]&&u[e][t][i]&&(u[e][t][i]=[])}else u[e][t]&&(u[e][t]=[]);Mt(),await Qe()}}let l={},Ue={};async function zt(){try{if(await mt())try{l=await un(),console.log("Quiz data loaded from backend API");return}catch(i){console.warn("Backend API failed, falling back to static files:",i)}const t=await fetch("./data/quizData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Response is not JSON, content-type:",o);const i=await t.text();throw console.error("Response text:",i.substring(0,200)),new Error("Invalid JSON response")}l=await t.json(),console.log("Quiz data loaded from static file")}catch(e){console.error("Failed to load quiz data:",e),l={"time-limited":{},"image-based":{},all:[]},console.warn("Using fallback empty quiz data structure")}}async function wn(){try{const e=await fetch("./data/learningData.json");if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=e.headers.get("content-type");if(!t||!t.includes("application/json")){console.warn("Learning data response is not JSON, content-type:",t);const o=await e.text();throw console.error("Response text:",o.substring(0,200)),new Error("Invalid JSON response for learning data")}Ue=await e.json(),console.log("Learning data loaded successfully")}catch(e){console.error("Failed to load learning data:",e),Ue={},console.warn("Using fallback empty learning data structure")}}async function In(){return await Promise.all([zt(),wn(),En()]),{quizData:l,learningData:Ue}}async function gt(){return console.log("🔄 Refreshing quiz data after admin changes..."),await zt(),console.log("✅ Quiz data refreshed successfully"),l}const V={soundEnabled:!0,currentTheme:"theme-standard"};function $t(e,t){["theme-standard","theme-dark"].forEach(a=>document.body.classList.remove(a));const i=e||"theme-standard";document.body.classList.add(i),V.currentTheme=i,localStorage.setItem("terraTueftlerTheme",i),t&&(t.value=i)}function Sn(e,t,o){const i=localStorage.getItem("terraTueftlerTheme")||"theme-standard";$t(i,e);const a=localStorage.getItem("terraTueftlerSoundEnabled");V.soundEnabled=a!==null?JSON.parse(a):!0,t&&(t.checked=V.soundEnabled),a===null&&localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(V.soundEnabled)),typeof o=="function"&&o()}function kn(e){V.soundEnabled=e,localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(V.soundEnabled))}function je(e){if(!(!V.soundEnabled||!window.AudioContext&&!window.webkitAudioContext))try{const t=new(window.AudioContext||window.webkitAudioContext);let o,i;switch(i=t.createGain(),i.connect(t.destination),i.gain.setValueAtTime(.5,t.currentTime),o=t.createOscillator(),o.connect(i),e){case"correct":o.type="sine",o.frequency.setValueAtTime(523.25,t.currentTime),i.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),o.start(t.currentTime),o.stop(t.currentTime+.3);break;case"incorrect":o.type="square",o.frequency.setValueAtTime(110,t.currentTime),i.gain.exponentialRampToValueAtTime(.001,t.currentTime+.4),o.start(t.currentTime),o.stop(t.currentTime+.4);break;case"quizEnd":const a=[261.63,329.63,391.99,523.25];let r=t.currentTime;a.forEach((c,d)=>{const m=t.createOscillator(),f=t.createGain();m.connect(f),f.connect(t.destination),m.type="triangle",m.frequency.setValueAtTime(c,r+d*.15),f.gain.setValueAtTime(.4,r+d*.15),f.gain.exponentialRampToValueAtTime(.001,r+d*.15+.1),m.start(r+d*.15),m.stop(r+d*.15+.1)});break}setTimeout(()=>{t.state!=="closed"&&t.close().catch(a=>console.warn("Error closing AudioContext:",a))},1e3)}catch(t){console.warn("AudioContext could not be started or used.",t),V.soundEnabled=!1;const o=document.getElementById("sound-toggle");o&&(o.checked=!1)}}console.log("🔧 QUIZ.JS LOADED - Version with question text fix - 2024-fix");const s={currentQuizType:"",currentCategory:"",currentQuestions:[],currentQuestionIndex:0,selectedAnswer:null,userAnswers:[],questionStates:[],score:0,currentStreak:0,maxStreak:0,quizEnded:!1,selectedTimeLimit:1,timerId:null,isTimerRunning:!1,imagePhaseActive:!1};let $e,Re,se,b,B,p,I,h,A,Ce,pe,ue,v,T,S,H,M,He,Ke,Ve,ie,ae,le;function ft(){$e=document.getElementById("quiz"),$e&&$e.querySelectorAll(".quiz-option[data-quiz-type]"),document.getElementById("time-limit-options"),document.getElementById("time-limit-select"),document.getElementById("quiz-category"),Re=document.querySelector("#quiz-category h2"),se=document.getElementById("quiz-category-options"),document.getElementById("back-to-quiz-selection"),b=document.getElementById("quiz-game"),B=document.getElementById("question-text"),p=document.getElementById("question-image"),I=document.getElementById("answer-options"),h=document.getElementById("feedback"),A=document.getElementById("timer"),Ce=document.getElementById("timer-phase"),pe=document.getElementById("time-left"),ue=document.getElementById("prev-question"),v=document.getElementById("submit-answer"),T=document.getElementById("next-question"),S=document.getElementById("quit-quiz"),H=document.getElementById("finish-quiz"),M=document.getElementById("scoreboard"),He=document.getElementById("score"),Ke=document.getElementById("final-streak"),Ve=document.getElementById("score-message"),ie=document.getElementById("streak-display"),ae=document.getElementById("current-streak"),document.getElementById("leaderboard"),document.getElementById("leaderboard-mode-select"),le=document.getElementById("leaderboard-list"),document.getElementById("clear-leaderboard")}function be(){clearInterval(s.timerId),s.currentQuizType="",s.currentCategory="",s.currentQuestions=[],s.currentQuestionIndex=0,s.selectedAnswer=null,s.userAnswers=[],s.questionStates=[],s.score=0,s.currentStreak=0,s.maxStreak=0,s.quizEnded=!1,s.timerId=null,s.isTimerRunning=!1,s.imagePhaseActive=!1,A&&(A.style.display="none"),h&&(h.style.display="none"),M&&(M.style.display="none"),ie&&(ie.style.display="none"),v&&(v.style.display="inline-block"),T&&(T.style.display="none"),ue&&(ue.style.display="none"),H&&(H.style.display="none"),S&&(S.style.display="none"),B&&(B.textContent="Frage wird geladen..."),p&&(p.style.display="none"),I&&(I.innerHTML="");const e=b?b.querySelector(".question-container"):null,t=b?b.querySelector(".quiz-controls"):null;e&&(e.style.display="block"),t&&(t.style.display="flex")}function Te(e){if(!se){console.error("Quiz category options container not found.");return}s.currentQuizType=e,se.innerHTML="";let t=ht(e);e==="time-limited"&&(t+=` (${s.selectedTimeLimit}s)`),Re&&(Re.textContent=`Wähle eine Kategorie (${t})`);let o=[];if(l.questions)o=Object.keys(l.questions);else{let i=e;if(e==="time-limited"&&(i="image-based"),!l[i]){console.error(`Quiz data source "${i}" not found in quizData.`),se.innerHTML="<p>Fehler: Quiztyp nicht gefunden.</p>",K("quiz-category");return}o=Object.keys(l[i])}o.length===0?se.innerHTML="<p>Keine Kategorien für diesen Modus verfügbar.</p>":o.forEach(i=>{const a=document.createElement("div");a.classList.add("quiz-option");const r=i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ");a.textContent=r,a.dataset.category=i,se.appendChild(a)}),K("quiz-category")}function _e(e,t){var r;be(),s.currentQuizType=e,s.currentCategory=t;let o=[];if(l.questions&&l.questions[t])o=l.questions[t];else{let c=e;if(e==="time-limited"&&(c="image-based"),!((r=l[c])!=null&&r[t])||l[c][t].length===0){alert("Fehler: Quiz konnte nicht geladen werden. Kategorie oder Typ ungültig."),K("quiz-category");return}o=l[c][t]}if(o.length===0){alert("Fehler: Keine Fragen in dieser Kategorie verfügbar."),K("quiz-category");return}s.currentQuestions=tn([...o]),s.userAnswers=new Array(s.currentQuestions.length).fill(null),s.questionStates=s.currentQuestions.map(()=>({answered:!1,locked:!1})),b||ft();const i=b==null?void 0:b.querySelector(".question-container"),a=b==null?void 0:b.querySelector(".quiz-controls");i&&(i.style.display="block"),a&&(a.style.display="flex"),B&&(B.style.display="block"),I&&(I.style.display="grid"),M&&(M.style.display="none"),ie&&(ie.style.display="block"),ae&&(ae.textContent=s.currentStreak),S&&(S.style.display="inline-block"),K("quiz-game"),pt(),e==="time-limited"&&A&&pe&&Ce?yt():A&&(A.style.display="none")}function pt(){if(!B||!I||!h||!v||!T||!ae||!S){console.error("Required quiz game elements not found for loading question.");return}if(s.quizEnded||s.currentQuestionIndex>=s.currentQuestions.length){Ae();return}const e=s.currentQuestions[s.currentQuestionIndex],t=s.questionStates[s.currentQuestionIndex];if(s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],console.log("DEBUG: Current question:",e),console.log("DEBUG: Question field exists:",!!e.question),console.log("DEBUG: Quiz type:",s.currentQuizType),console.log("DEBUG: questionTextElement exists:",!!B),!B&&(console.error("ERROR: questionTextElement is null! Re-caching DOM elements..."),ft(),!B)){console.error("ERROR: questionTextElement still null after re-caching!");return}if(s.currentQuizType==="time-limited")B.style.display="none",console.log("DEBUG: Time-limited quiz - hiding question text");else{if(B.style.display="block",e.question){const a=`Frage ${s.currentQuestionIndex+1}: ${e.question}`;B.textContent=a,console.log("DEBUG: Using original question text:",a)}else{const a=`Frage ${s.currentQuestionIndex+1}: In welchem Land befindet sich das?`;B.textContent=a,console.log("DEBUG: Using fallback question text:",a)}console.log("DEBUG: Final question element text:",B.textContent),console.log("DEBUG: Final question element innerHTML:",B.innerHTML)}I.innerHTML="",h.style.display="none",h.textContent="",ae.textContent=s.currentStreak;const o=t.locked;if(o){if(v.style.display="none",T.style.display="inline-block",S.style.display="inline-block",I.style.pointerEvents="none",t.answered){h.style.display="block";const r=s.userAnswers[s.currentQuestionIndex]===e.correctAnswer;h.textContent=r?"✓ Richtig! Diese Antwort ist gesperrt.":"✗ Falsch. Diese Antwort ist gesperrt.",h.className=r?"feedback correct":"feedback incorrect"}}else v.style.display="inline-block",T.style.display="none",S.style.display="inline-block",I.style.pointerEvents="auto";if(p&&e.image)if(p.src=e.image,p.style.display="block",e.question?p.alt=`Bild zur Frage: ${e.question}`:p.alt=`Bild zur Frage ${s.currentQuestionIndex+1}`,e.streetViewUrl){p.classList.remove("streetview-locked","streetview-unlocked","streetview-unlocking"),p.classList.add("streetview-available"),p.removeEventListener("click",Ie),p.removeEventListener("click",Se);const a=s.questionStates[s.currentQuestionIndex];a&&a.locked?(p.classList.add("streetview-unlocked"),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",Ie)):(p.classList.add("streetview-locked"),p.style.cursor="not-allowed",p.title="Beantworte die Frage, um Street View zu entsperren",p.addEventListener("click",Se))}else p.classList.remove("streetview-available","streetview-locked","streetview-unlocked","streetview-unlocking"),p.style.cursor="default",p.title="",p.removeEventListener("click",Ie),p.removeEventListener("click",Se);else p&&(p.style.display="none");tn([...e.options]).forEach(a=>{const r=document.createElement("div");r.classList.add("answer-option"),r.textContent=a,r.dataset.option=a,o?(r.classList.add("locked"),a===e.correctAnswer?r.classList.add("correct"):a===s.selectedAnswer&&r.classList.add("incorrect"),a===s.selectedAnswer&&r.classList.add("selected")):(a===s.selectedAnswer&&r.classList.add("selected"),r.addEventListener("click",Bn)),I.appendChild(r)}),Nt()}function Bn(e){if(!s.questionStates[s.currentQuestionIndex].locked&&v&&v.style.display!=="none"){const o=e.target.dataset.option;s.selectedAnswer=o,s.userAnswers[s.currentQuestionIndex]=o,document.querySelectorAll("#answer-options .answer-option").forEach(i=>{i.classList.remove("selected")}),e.target.classList.add("selected")}}function Ie(e){e.preventDefault();const t=s.currentQuestions[s.currentQuestionIndex];t&&t.streetViewUrl&&(window.open(t.streetViewUrl,"_blank","noopener,noreferrer"),g("Street View-Location wird geöffnet...","info"))}function Se(e){e.preventDefault(),g("Beantworte zuerst die Frage, um Street View zu entsperren!","warning")}function Ln(){if(!p)return;const e=s.currentQuestions[s.currentQuestionIndex];e&&e.streetViewUrl&&p.classList.contains("streetview-locked")&&(p.classList.remove("streetview-locked"),p.classList.add("streetview-unlocking"),p.removeEventListener("click",Se),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",Ie),setTimeout(()=>{p.classList.remove("streetview-unlocking"),p.classList.add("streetview-unlocked")},600))}function Nt(){if(!ue||!H||!S||!v||!T)return;ue.style.display=s.currentQuestionIndex>0?"inline-block":"none",S&&(S.style.display="inline-block"),v.style.display==="none"?(T.style.display=s.currentQuestionIndex<s.currentQuestions.length-1?"inline-block":"none",H.style.display=s.currentQuestionIndex>=s.currentQuestions.length-1?"inline-block":"none"):(T.style.display="none",H.style.display="none")}function qn(e=!1){if(console.log("checkAnswer called with isTimeout:",e,"current score:",s.score,"current streak:",s.currentStreak),v&&v.style.display==="none"&&!e){console.log("Answer already submitted for this question, ignoring duplicate call");return}if(!v||!h||!I||!T||!H||!ae||!S){console.error("Required quiz elements not found for checking answer.");return}if(!e&&!s.selectedAnswer){g("Bitte wähle zuerst eine Antwort aus.","warning");return}clearInterval(s.timerId),s.isTimerRunning=!1,v.style.display="none",I.style.pointerEvents="none";const t=s.currentQuestions[s.currentQuestionIndex],o=!e&&s.selectedAnswer===t.correctAnswer;Ln(),o?(s.quizEnded||(console.log("Score before increment:",s.score),s.score++,console.log("Score after increment:",s.score),console.log("Streak before increment:",s.currentStreak),s.currentStreak++,console.log("Streak after increment:",s.currentStreak),s.maxStreak=Math.max(s.maxStreak,s.currentStreak),console.log("Max streak updated to:",s.maxStreak)),h.textContent=`Richtig! ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-correct)",je("correct")):(e?h.textContent=`Zeit abgelaufen! Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`:h.textContent=`Falsch. Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-incorrect)",je("incorrect")),ae.textContent=s.currentStreak,document.querySelectorAll("#answer-options .answer-option").forEach(i=>{i.classList.remove("selected"),i.dataset.option===t.correctAnswer?i.classList.add("correct"):i.dataset.option===s.selectedAnswer&&!o&&i.classList.add("incorrect")}),h.style.display="block",s.questionStates[s.currentQuestionIndex].answered=!0,Nt()}function Cn(){!T||!v||!h||!S||(s.questionStates[s.currentQuestionIndex]&&(s.questionStates[s.currentQuestionIndex].locked=!0),s.currentQuestionIndex<s.currentQuestions.length-1?(s.currentQuestionIndex++,s.selectedAnswer=null,h.style.display="none",v.style.display="inline-block",T.style.display="none",S.style.display="inline-block",I&&(I.style.pointerEvents="auto"),pt(),s.currentQuizType==="time-limited"&&yt()):Ae())}function Tn(){clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,!(!ue||!v||!T||!H||!h||!S)&&s.currentQuestionIndex>0&&(s.currentQuestionIndex--,s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],h.style.display="none",v.style.display="inline-block",T.style.display="none",H.style.display="none",S.style.display="inline-block",I&&(I.style.pointerEvents="auto"),pt(),s.currentQuizType==="time-limited"&&yt())}async function Ae(){if(console.log("finishQuiz called. Current quiz state:",JSON.stringify(s)),clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,s.quizEnded=!0,y.playerName&&y.playerName!=="Anonym"){const o=s.currentQuizType==="time-limited"?s.selectedTimeLimit:null;await yn(y.playerName,s.score,s.currentQuizType,s.currentCategory,s.currentQuestions.length,s.maxStreak,o)}const e=s.currentQuestionIndex<s.currentQuestions.length,t=s.currentQuizType;s.currentCategory,e&&(!M||M.style.display==="none")?(console.log("Finishing mid-quiz, going to category selection."),be(),Te(t||"image-based")):(console.log("Quiz ended naturally or from score screen, showing score."),An())}function An(){if(!M||!He||!Ke||!Ve){console.error("Scoreboard elements not found.");return}const e=b==null?void 0:b.querySelector(".question-container"),t=b==null?void 0:b.querySelector(".quiz-controls");e&&(e.style.display="none"),A&&(A.style.display="none"),h&&(h.style.display="none"),t&&(t.style.display="none"),ie&&(ie.style.display="none"),He.textContent=`${s.score} / ${s.currentQuestions.length}`,Ke.textContent=s.maxStreak;let o="";const i=s.currentQuestions.length>0?s.score/s.currentQuestions.length:0;if(s.maxStreak>=10?o=`Wow, ${s.maxStreak} in Folge! Spitzenleistung!`:s.maxStreak>=5?o=`Starke Serie von ${s.maxStreak}! Gut gemacht!`:i>=.7?o="Sehr gutes Ergebnis!":i>=.5?o="Gut gemacht!":o="Übung macht den Meister! Schau doch mal im Lernbereich vorbei.",Ve.textContent=o,M.style.display="block",je("quizEnd"),M){let a=M.querySelector(".scoreboard-actions");a||(a=document.createElement("div"),a.className="scoreboard-actions",M.appendChild(a)),a.innerHTML="";const r=document.createElement("button");r.textContent="Erneut spielen (Zufall)",r.className="btn",r.id="play-again-random-btn",r.addEventListener("click",()=>{_e(s.currentQuizType,s.currentCategory)}),a.appendChild(r);const c=document.createElement("button");c.textContent="Fortfahren",c.className="btn",c.id="fortfahren-btn",c.addEventListener("click",()=>{_e(s.currentQuizType,s.currentCategory)}),a.appendChild(c);const d=document.createElement("button");d.textContent="Quiz beenden",d.className="btn",d.id="scoreboard-main-menu-btn",d.addEventListener("click",()=>{be(),K("home")}),a.appendChild(d)}}function yt(){if(!A||!pe||!Ce)return;s.imagePhaseActive=!0,A.style.display="block",A.style.background="var(--clr-primary)",Ce.textContent="Bild sichtbar:";let e=s.selectedTimeLimit;pe.textContent=e.toFixed(1),clearInterval(s.timerId),s.isTimerRunning=!0,p&&(p.style.display="block");const t=e<1?100:1e3,o=e<1?.1:1;s.timerId=setInterval(()=>{e-=o,e<=0?(e=0,clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,p&&(p.style.display="none",g("Bild ausgeblendet - Wähle das Land!","info")),A&&(A.style.display="none")):pe.textContent=e<1?e.toFixed(1):Math.ceil(e)},t)}function xn(e,t){if(l.questions&&l.questions[t])return l.questions[t].length;{let o=e;return e==="time-limited"&&(o="image-based"),!l[o]||!l[o][t]?0:l[o][t].length}}function Qn(e,t,o=null){let i=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&o&&(i+=`_${o}s`),i}function Ft(e,t="all",o=null){if(!le||!e){le||console.error("Leaderboard list element not found.");return}const i=Qt(e,t,o),a=xn(e,t);if(le.innerHTML="",i.length===0){const r=ht(e),c=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),d=o?` (${o}s)`:"";le.innerHTML=`<li style="text-align: center; color: var(--clr-text-light);">Noch keine Einträge für "${r} - ${c}${d}" vorhanden. Spiel ein Quiz!</li>`;return}i.forEach((r,c)=>{const d=document.createElement("li");d.classList.add("leaderboard-item");const m=a>0&&r.correctAnswers>=a;m&&d.classList.add("perfect-score");const f=document.createElement("span");f.classList.add("leaderboard-rank"),f.textContent=`${c+1}.`;const E=document.createElement("span");E.classList.add("leaderboard-name");const O=m?"⭐ ":"";E.textContent=O+r.name;const Q=document.createElement("span");Q.classList.add("leaderboard-score");const U=r.totalQuestions>0?Math.round(r.correctAnswers/r.totalQuestions*100):0;Q.textContent=`${r.correctAnswers}/${r.totalQuestions} (${U}%)`;const j=document.createElement("span");j.classList.add("leaderboard-streak"),r.maxStreak!==void 0&&(j.textContent=`Serie: ${r.maxStreak}`);const z=document.createElement("span");if(z.classList.add("leaderboard-time"),r.completedAt){const $=new Date(r.completedAt),w=$.toLocaleDateString("de-DE")+" "+$.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});z.textContent=w}else if(r.lastPlayed){const $=new Date(r.lastPlayed),w=$.toLocaleDateString("de-DE")+" "+$.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});z.textContent=w}d.appendChild(f),d.appendChild(E),d.appendChild(Q),r.maxStreak!==void 0&&d.appendChild(j),z.textContent&&d.appendChild(z),le.appendChild(d)})}async function Mn(e,t="all",o=null){if(!e)return;const i=ht(e),a=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),r=o?` (${o}s)`:"",c=`${i} - ${a}${r}`;if(confirm(`Möchtest du die Rangliste für "${c}" wirklich löschen?`)){await vn(e,t,o);const d=Qn(e,t,o);localStorage.removeItem(d),Ft(e,t,o)}}function ht(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}const k={isInitialized:!1,dragDropInitialized:!1,currentStats:{totalCategories:0,totalQuestions:0,totalModes:2}};let n={},oe=!1;function Ot(){n={adminSection:document.getElementById("admin"),addCategoryBtn:document.getElementById("add-category-btn"),addQuestionBtn:document.getElementById("add-question-btn"),manageContentBtn:document.getElementById("manage-content-btn"),exportDataBtn:document.getElementById("export-data-btn"),importDataBtn:document.getElementById("import-data-btn"),totalCategoriesSpan:document.getElementById("total-categories"),totalQuestionsSpan:document.getElementById("total-questions"),totalModesSpan:document.getElementById("total-modes"),totalLeaderboardEntriesSpan:document.getElementById("total-leaderboard-entries"),totalPlayersSpan:document.getElementById("total-players"),clearLeaderboardBtn:document.getElementById("clear-leaderboard-btn"),leaderboardStatsBtn:document.getElementById("leaderboard-stats-btn"),leaderboardStatsModal:document.getElementById("leaderboard-stats-modal"),closeStatsModal:document.getElementById("close-stats-modal"),closeStatsBtn:document.getElementById("close-stats"),statsTotalEntries:document.getElementById("stats-total-entries"),statsTotalPlayers:document.getElementById("stats-total-players"),statsLastUpdated:document.getElementById("stats-last-updated"),topPerformersList:document.getElementById("top-performers-list"),categoryStats:document.getElementById("category-stats"),addCategoryModal:document.getElementById("add-category-modal"),closeCategoryModal:document.getElementById("close-category-modal"),categoryNameInput:document.getElementById("category-name"),modeImageBased:document.getElementById("mode-image-based"),modeTimeLimited:document.getElementById("mode-time-limited"),categoryDescriptionInput:document.getElementById("category-description"),cancelCategoryBtn:document.getElementById("cancel-category"),confirmCategoryBtn:document.getElementById("confirm-category"),addQuestionModal:document.getElementById("add-question-modal"),closeQuestionModal:document.getElementById("close-question-modal"),questionCategorySelect:document.getElementById("question-category"),questionTextInput:document.getElementById("admin-question-text"),questionImageInput:document.getElementById("question-image"),questionImageFileInput:document.getElementById("question-image-file"),imagePreview:document.getElementById("image-preview"),previewImg:document.getElementById("preview-img"),imageFilename:document.getElementById("image-filename"),imageSize:document.getElementById("image-size"),option1Input:document.getElementById("option-1"),option2Input:document.getElementById("option-2"),option3Input:document.getElementById("option-3"),option4Input:document.getElementById("option-4"),correctAnswerSelect:document.getElementById("correct-answer"),questionExplanationInput:document.getElementById("question-explanation"),questionStreetViewUrlInput:document.getElementById("question-streetview-url"),questionPreview:document.getElementById("question-preview"),previewQuestionBtn:document.getElementById("preview-question"),cancelQuestionBtn:document.getElementById("cancel-question"),confirmQuestionBtn:document.getElementById("confirm-question"),contentManagementModal:document.getElementById("content-management-modal"),closeContentModal:document.getElementById("close-content-modal"),categoriesTab:document.getElementById("categories-tab"),questionsTab:document.getElementById("questions-tab"),categoriesContent:document.getElementById("categories-content"),questionsContent:document.getElementById("questions-content"),categoriesList:document.getElementById("categories-list"),questionsList:document.getElementById("questions-list"),filterMode:document.getElementById("filter-mode"),filterCategory:document.getElementById("filter-category"),closeContentBtn:document.getElementById("close-content"),deleteConfirmationModal:document.getElementById("delete-confirmation-modal"),closeDeleteModal:document.getElementById("close-delete-modal"),deleteModalTitle:document.getElementById("delete-modal-title"),deleteModalMessage:document.getElementById("delete-modal-message"),deleteModalDetails:document.getElementById("delete-modal-details"),cancelDeleteBtn:document.getElementById("cancel-delete"),confirmDeleteBtn:document.getElementById("confirm-delete"),deleteBtnText:document.getElementById("delete-btn-text"),deleteLoading:document.getElementById("delete-loading")}}function Dn(){k.isInitialized||(console.log("🔧 Initializing admin interface..."),Ot(),console.log("=== ADMIN ELEMENTS DEBUG ==="),Object.keys(n).forEach(e=>{console.log(`${e}:`,n[e]?"✅ Found":"❌ Not found")}),zn(),Pt(),Ut(),Jt(),Gt(),Yt(),Zn()&&(console.log("Data migration needed - migrating to unified structure..."),Gn().then(e=>{e?(console.log("Data migration completed successfully"),g("Datenstruktur wurde automatisch aktualisiert und optimiert!","success")):(console.error("Data migration failed"),g("Warnung: Datenstruktur-Update fehlgeschlagen","warning")),Z()})),Z(),k.isInitialized=!0,console.log("Admin interface initialized"))}function zn(){n.addCategoryBtn&&n.addCategoryBtn.addEventListener("click",$n),n.addQuestionBtn&&n.addQuestionBtn.addEventListener("click",Rt),n.manageContentBtn&&n.manageContentBtn.addEventListener("click",Wn),n.exportDataBtn&&n.exportDataBtn.addEventListener("click",Hn),n.importDataBtn&&n.importDataBtn.addEventListener("click",Kn),n.clearLeaderboardBtn&&n.clearLeaderboardBtn.addEventListener("click",_n),n.leaderboardStatsBtn&&n.leaderboardStatsBtn.addEventListener("click",Jn),Jt(),Pt(),Ut(),Gt(),Yt()}function Pt(){n.closeCategoryModal&&n.closeCategoryModal.addEventListener("click",ke),n.cancelCategoryBtn&&n.cancelCategoryBtn.addEventListener("click",ke),n.confirmCategoryBtn&&n.confirmCategoryBtn.addEventListener("click",Un),n.addCategoryModal&&n.addCategoryModal.addEventListener("click",e=>{e.target===n.addCategoryModal&&ke()})}function Ut(){n.closeQuestionModal&&n.closeQuestionModal.addEventListener("click",Be),n.cancelQuestionBtn&&n.cancelQuestionBtn.addEventListener("click",Be),n.confirmQuestionBtn&&n.confirmQuestionBtn.addEventListener("click",Rn),n.previewQuestionBtn&&n.previewQuestionBtn.addEventListener("click",_t),n.questionImageInput&&n.questionImageInput.addEventListener("input",Kt),n.questionImageFileInput&&n.questionImageFileInput.addEventListener("change",Fn),[n.option1Input,n.option2Input,n.option3Input,n.option4Input].forEach(t=>{t&&t.addEventListener("input",Vt)}),n.addQuestionModal&&n.addQuestionModal.addEventListener("click",t=>{t.target===n.addQuestionModal&&Be()})}function Z(){let e=0,t=0;Object.keys(l).forEach(i=>{const a=Object.keys(l[i]);e+=a.length,a.forEach(r=>{t+=l[i][r].length})});const o=new Set;Object.keys(l).forEach(i=>{Object.keys(l[i]).forEach(a=>{o.add(a)})}),k.currentStats.totalCategories=o.size,k.currentStats.totalQuestions=t;try{const i=Dt();k.currentStats.totalLeaderboardEntries=i.totalEntries,k.currentStats.totalPlayers=i.totalPlayers}catch(i){console.error("Error getting leaderboard stats:",i),k.currentStats.totalLeaderboardEntries=0,k.currentStats.totalPlayers=0}n.totalCategoriesSpan&&(n.totalCategoriesSpan.textContent=k.currentStats.totalCategories),n.totalQuestionsSpan&&(n.totalQuestionsSpan.textContent=k.currentStats.totalQuestions),n.totalModesSpan&&(n.totalModesSpan.textContent=k.currentStats.totalModes),n.totalLeaderboardEntriesSpan&&(n.totalLeaderboardEntriesSpan.textContent=k.currentStats.totalLeaderboardEntries),n.totalPlayersSpan&&(n.totalPlayersSpan.textContent=k.currentStats.totalPlayers)}function $n(){n.addCategoryModal&&(jt(),n.addCategoryModal.style.display="flex",n.categoryNameInput&&n.categoryNameInput.focus())}function ke(){n.addCategoryModal&&(n.addCategoryModal.style.display="none",jt())}function jt(){n.categoryNameInput&&(n.categoryNameInput.value=""),n.categoryDescriptionInput&&(n.categoryDescriptionInput.value=""),n.modeImageBased&&(n.modeImageBased.checked=!1),n.modeTimeLimited&&(n.modeTimeLimited.checked=!1)}function Rt(){n.addQuestionModal&&(Ht(),Nn(),n.addQuestionModal.style.display="flex",n.questionCategorySelect&&n.questionCategorySelect.focus(),k.dragDropInitialized||setTimeout(()=>{Pn(),k.dragDropInitialized=!0},200))}function Be(){n.addQuestionModal&&(n.addQuestionModal.style.display="none",Ht())}function Ht(){oe=!1,N={isEditing:!1,mode:null,category:null,questionIndex:null,originalQuestion:null};const e=document.querySelector("#add-question-modal .modal-header h3");e&&(e.textContent="Neue Frage hinzufügen");const t=n.confirmQuestionBtn;t&&(t.textContent="Frage hinzufügen",t.disabled=!1),n.questionCategorySelect&&(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',n.questionCategorySelect.disabled=!1),n.questionTextInput&&(n.questionTextInput.value=""),n.questionImageInput&&(n.questionImageInput.value=""),n.questionImageFileInput&&(n.questionImageFileInput.value=""),ce(),n.option1Input&&(n.option1Input.value=""),n.option2Input&&(n.option2Input.value=""),n.option3Input&&(n.option3Input.value=""),n.option4Input&&(n.option4Input.value=""),n.correctAnswerSelect&&(n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>'),n.questionExplanationInput&&(n.questionExplanationInput.value=""),n.questionStreetViewUrlInput&&(n.questionStreetViewUrlInput.value=""),n.questionPreview&&(n.questionPreview.style.display="none")}function Nn(){if(!n.questionCategorySelect)return;n.questionCategorySelect.innerHTML="";let e=[];if(l.questions)e=Object.keys(l.questions).filter(t=>t!=="all");else{const t=new Set;["image-based","time-limited"].forEach(o=>{l[o]&&Object.keys(l[o]).forEach(i=>{i!=="all"&&t.add(i)})}),e=Array.from(t)}console.log("=== CATEGORY LOADING DEBUG ==="),console.log("Available categories:",e),console.log("Using unified structure:",!!l.questions),e.length===0?(n.questionCategorySelect.innerHTML='<option value="">Keine Kategorien verfügbar</option>',n.questionCategorySelect.disabled=!0):(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',e.sort().forEach(t=>{const o=document.createElement("option");o.value=t,o.textContent=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),n.questionCategorySelect.appendChild(o)}),n.questionCategorySelect.disabled=!1)}function Fn(e){if(oe){console.log("File upload already in progress, ignoring duplicate event");return}oe=!0;try{const t=e.target.files[0];if(!t){ce();return}if(!t.type.startsWith("image/")){g("Bitte wähle eine gültige Bilddatei aus.","warning"),e.target.value="",ce();return}if(t.size>10*1024*1024){g("Bilddatei ist zu groß. Maximum: 10MB","warning"),e.target.value="",ce();return}n.questionImageInput&&(n.questionImageInput.value="");const o=new FileReader;o.onload=i=>{Et(i.target.result,t.name,t.size)},o.readAsDataURL(t)}finally{setTimeout(()=>{oe=!1},100)}}function Kt(){if(!n.questionImageInput||!n.imagePreview||!n.previewImg)return;const e=n.questionImageInput.value.trim();e&&te(e)?(n.questionImageFileInput&&(n.questionImageFileInput.value=""),n.previewImg.src=e,n.previewImg.onload=()=>{Et(e,"Externe URL",null)},n.previewImg.onerror=()=>{ce(),g("Bild konnte nicht geladen werden. Überprüfe die URL.","warning")}):ce()}function Et(e,t,o){!n.imagePreview||!n.previewImg||(n.previewImg.src=e,n.imagePreview.style.display="block",n.imageFilename&&(n.imageFilename.textContent=t),n.imageSize&&o?n.imageSize.textContent=On(o):n.imageSize&&(n.imageSize.textContent=""))}function ce(){n.imagePreview&&(n.imagePreview.style.display="none")}function On(e){if(e===0)return"0 Bytes";const t=1024,o=["Bytes","KB","MB","GB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+o[i]}function Pn(){const e=document.querySelector(".file-upload-label"),t=n.addQuestionModal;if(console.log("🔧 Setting up drag and drop..."),console.log("File upload label found:",!!e),console.log("Add question modal found:",!!t),!e||!t){console.warn("❌ Drag and drop setup failed - missing elements");return}console.log("✅ Drag and drop setup successful"),["dragenter","dragover","dragleave","drop"].forEach(d=>{document.addEventListener(d,o,!1),t.addEventListener(d,o,!1)}),["dragenter","dragover"].forEach(d=>{e.addEventListener(d,i,!1)}),["dragleave","drop"].forEach(d=>{e.addEventListener(d,a,!1)}),e.addEventListener("drop",r,!1),t.addEventListener("drop",c,!1);function o(d){d.preventDefault(),d.stopPropagation()}function i(d){console.log("🎯 Drag enter/over detected"),e.classList.add("drag-over")}function a(d){console.log("🎯 Drag leave/drop detected"),e.classList.remove("drag-over")}function r(d){if(console.log("📁 Drop event triggered"),oe){console.log("File upload already in progress, ignoring drop event");return}const f=d.dataTransfer.files;if(console.log("Files dropped:",f.length),f.length>0){const E=f[0];if(!E.type.startsWith("image/")){g("Bitte ziehe eine gültige Bilddatei hierher.","warning");return}if(E.size>10*1024*1024){g("Bilddatei ist zu groß. Maximum: 10MB","warning");return}if(oe=!0,n.questionImageFileInput){const O=new DataTransfer;O.items.add(E),n.questionImageFileInput.files=O.files,n.questionImageInput&&(n.questionImageInput.value="");const Q=new FileReader;Q.onload=U=>{Et(U.target.result,E.name,E.size),setTimeout(()=>{oe=!1},100)},Q.readAsDataURL(E)}g("Bild erfolgreich hochgeladen!","success")}}function c(d){d.target.closest(".file-upload-label")||(console.log("📁 Modal drop event triggered"),r(d))}}function Vt(){var o,i,a,r;if(!n.correctAnswerSelect)return;const e=[(o=n.option1Input)==null?void 0:o.value.trim(),(i=n.option2Input)==null?void 0:i.value.trim(),(a=n.option3Input)==null?void 0:a.value.trim(),(r=n.option4Input)==null?void 0:r.value.trim()].filter(c=>c&&c.length>0),t=n.correctAnswerSelect.value;n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>',e.forEach(c=>{const d=document.createElement("option");d.value=c,d.textContent=c,n.correctAnswerSelect.appendChild(d)}),t&&e.includes(t)&&(n.correctAnswerSelect.value=t)}function te(e){try{return new URL(e),!0}catch{return!1}}async function Un(){var a,r,c,d;const e=(a=n.categoryNameInput)==null?void 0:a.value.trim();(r=n.categoryDescriptionInput)==null||r.value.trim();const t=[];if((c=n.modeImageBased)!=null&&c.checked&&t.push("image-based"),(d=n.modeTimeLimited)!=null&&d.checked&&t.push("time-limited"),!e){g("Bitte gib einen Kategorie-Namen ein.","warning");return}if(!jn(e)){g("Kategorie-Name darf nur Kleinbuchstaben, Zahlen und Unterstriche enthalten.","warning");return}if(t.length===0){g("Bitte wähle mindestens einen Quiz-Modus aus.","warning");return}const o=[];if(t.forEach(m=>{let f=m;m==="time-limited"&&(f="image-based"),l[f]&&l[f][e]&&o.push(m)}),o.length>0){const m=o.join(", ");g(`Kategorie "${e}" existiert bereits in: ${m}`,"warning");return}t.forEach(m=>{let f=m;m==="time-limited"&&(f="image-based"),l[f]||(l[f]={}),l[f][e]=[]});try{await dn(e,t)?console.log("Category saved to backend successfully"):console.warn("Failed to save category to backend, using localStorage backup")}catch(m){console.error("Error saving category to backend:",m)}ve(),Z(),ke();const i=t.join(", ");g(`Kategorie "${e}" erfolgreich zu ${i} hinzugefügt!`,"success")}function jn(e){return/^[a-z0-9_]+$/.test(e)}async function Rn(e){var E,O,Q,U,j,z,$,w,De,ze,wt;if(e&&(e.preventDefault(),e.stopPropagation()),n.confirmQuestionBtn){if(n.confirmQuestionBtn.disabled){console.log("Question submission already in progress, ignoring duplicate request");return}n.confirmQuestionBtn.disabled=!0,n.confirmQuestionBtn.textContent=N.isEditing?"Wird gespeichert...":"Wird hinzugefügt..."}const t=(E=n.questionCategorySelect)==null?void 0:E.value,o=(O=n.questionTextInput)==null?void 0:O.value.trim(),i=(Q=n.questionImageInput)==null?void 0:Q.value.trim(),a=(U=n.questionImageFileInput)==null?void 0:U.files[0],r=(j=n.questionExplanationInput)==null?void 0:j.value.trim(),c=(z=n.questionStreetViewUrlInput)==null?void 0:z.value.trim(),d=[($=n.option1Input)==null?void 0:$.value.trim(),(w=n.option2Input)==null?void 0:w.value.trim(),(De=n.option3Input)==null?void 0:De.value.trim(),(ze=n.option4Input)==null?void 0:ze.value.trim()].filter(L=>L&&L.length>0),m=(wt=n.correctAnswerSelect)==null?void 0:wt.value,f=()=>{n.confirmQuestionBtn&&(n.confirmQuestionBtn.disabled=!1,n.confirmQuestionBtn.textContent=N.isEditing?"Änderungen speichern":"Frage hinzufügen")};try{if(console.log("=== QUESTION SUBMISSION DEBUG ==="),console.log("Category:",t),console.log("Question Text:",o),console.log("Image URL:",i),console.log("Image File:",a),console.log("Options:",d),console.log("Correct Answer:",m),console.log("Explanation:",r),console.log("Street View URL:",c),!t){g("Bitte wähle eine Kategorie aus.","warning"),f();return}if(t.toLowerCase()==="all"){g('Die "All"-Kategorie wird automatisch verwaltet. Bitte wähle eine spezifische Kategorie.',"warning"),f();return}if(!a&&(!i||!te(i))){g("Ein Bild (Upload oder URL) ist für alle Fragen erforderlich.","warning"),f();return}if(d.length<2){g("Bitte gib mindestens 2 Antwortmöglichkeiten ein.","warning"),f();return}if(!m){g("Bitte wähle die richtige Antwort aus.","warning"),f();return}if(!d.includes(m)){g("Die richtige Antwort muss eine der Antwortmöglichkeiten sein.","warning"),f();return}const L={options:d,correctAnswer:m};o&&(L.question=o),r&&(L.explanation=r),c&&te(c)&&(L.streetViewUrl=c),i&&te(i)&&(L.image=i);const P=new FormData;P.append("mode","both"),P.append("category",t),P.append("questionData",JSON.stringify(L));const It=Date.now()+"_"+Math.random().toString(36).substring(2,11);P.append("requestId",It),a&&(!i||!te(i))&&(P.append("image",a),console.log("Adding image file to form data:",a.name,"size:",a.size)),console.log("Submitting question with request ID:",It),console.log("Form data contents:");for(let[q,Y]of P.entries())console.log(`  ${q}:`,Y);let St="/api/add-question",kt="POST";N.isEditing&&(St="/api/edit-question",kt="PUT",P.append("mode",N.mode),P.set("category",N.category),P.append("questionIndex",N.questionIndex.toString()));const X=await fetch(St,{method:kt,body:P});if(console.log("Response status:",X.status),console.log("Response ok:",X.ok),!X.ok){const q=await X.text();console.error("Server error response:",q);let Y;try{Y=JSON.parse(q)}catch{Y={error:q||"Unknown server error"}}throw new Error(Y.error||`HTTP ${X.status}: ${X.statusText}`)}const ge=await X.json();if(console.log("Question added successfully:",ge),ge.imagePath&&(L.image=ge.imagePath),N.isEditing){console.log("Edit completed, refreshing data from server..."),await gt(),console.log("Quiz data refreshed from server after edit"),N={isEditing:!1,mode:null,category:null,questionIndex:null,originalQuestion:null};const q=ge.imagePath?" (Bild automatisch organisiert)":"";g(`Frage erfolgreich bearbeitet!${q}`,"success")}else{l.questions||(l.questions={}),l.questions[t]||(l.questions[t]=[]),l.questions[t].push(L);let q=mode;mode==="time-limited"&&(q="image-based"),l[q]||(l[q]={}),l[q][t]||(l[q][t]=[]),l[q][t].push(L);const Y=ge.imagePath?" (Bild automatisch organisiert)":"";g(`Frage erfolgreich zu "${t}" (beide Quiz-Modi) hinzugefügt!${Y}`,"success")}ve(),Z(),Me(),Be()}catch(L){console.error("Error adding question:",L),g(`Fehler beim Hinzufügen der Frage: ${L.message}`,"error")}finally{f()}}function _t(){var m,f,E,O,Q,U,j,z,$;if(!n.questionPreview)return;const e=(m=n.questionTextInput)==null?void 0:m.value.trim(),t=(f=n.questionImageInput)==null?void 0:f.value.trim(),o=(E=n.questionImageFileInput)==null?void 0:E.files[0],i=(O=n.questionStreetViewUrlInput)==null?void 0:O.value.trim(),a=[(Q=n.option1Input)==null?void 0:Q.value.trim(),(U=n.option2Input)==null?void 0:U.value.trim(),(j=n.option3Input)==null?void 0:j.value.trim(),(z=n.option4Input)==null?void 0:z.value.trim()].filter(w=>w&&w.length>0),r=($=n.correctAnswerSelect)==null?void 0:$.value;let c="";if(e&&(c+=`<div style="margin-bottom: 1rem; font-weight: 600;">${e}</div>`),t&&te(t))c+=`<img src="${t}" alt="Frage-Bild" class="preview-image">`;else if(o){const w=n.previewImg;w&&w.src&&(c+=`<img src="${w.src}" alt="Frage-Bild" class="preview-image">`)}a.length>0&&(c+='<div class="preview-options">',a.forEach(w=>{c+=`<div class="${w===r?"preview-option correct":"preview-option"}">${w}</div>`}),c+="</div>"),i&&te(i)&&(c+=`<div style="margin-top: 1rem; padding: 0.5rem; background: #e8f4fd; border-radius: 4px; font-size: 0.9rem;">
            🌍 Street View verfügbar: Spieler können nach der Antwort die Location besuchen
        </div>`);const d=n.questionPreview.querySelector(".preview-content");d&&(d.innerHTML=c),n.questionPreview.style.display="block"}function Hn(){try{const e=JSON.stringify(l,null,2),t=new Blob([e],{type:"application/json"}),o=document.createElement("a");o.href=URL.createObjectURL(t),o.download=`terraTueftler-quizData-${new Date().toISOString().split("T")[0]}.json`,o.click(),g("Quiz-Daten erfolgreich exportiert!","success")}catch(e){console.error("Export error:",e),g("Fehler beim Exportieren der Daten.","error")}}function Kn(){const e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=t=>{const o=t.target.files[0];if(!o)return;const i=new FileReader;i.onload=a=>{try{const r=JSON.parse(a.target.result);if(!Vn(r)){g("Ungültige Datenstruktur. Bitte überprüfe die JSON-Datei.","error");return}confirm("Möchtest du die aktuellen Quiz-Daten durch die importierten Daten ersetzen? Diese Aktion kann nicht rückgängig gemacht werden.")&&(ve(),Object.keys(r).forEach(c=>{l[c]=r[c]}),Z(),g("Quiz-Daten erfolgreich importiert!","success"))}catch(r){console.error("Import error:",r),g("Fehler beim Importieren der Daten. Überprüfe das JSON-Format.","error")}},i.readAsText(o)},e.click()}function Vn(e){if(typeof e!="object"||e===null)return!1;const t=["image-based","time-limited"];for(const o in e)if(t.includes(o)){if(typeof e[o]!="object"||e[o]===null)return!1;for(const i in e[o]){if(!Array.isArray(e[o][i]))return!1;for(const a of e[o][i])if(!a.options||!Array.isArray(a.options)||a.options.length<2||!a.correctAnswer||!a.options.includes(a.correctAnswer))return!1}}return!0}function ve(){try{const t={timestamp:new Date().toISOString(),data:l};localStorage.setItem("terraTueftlerQuizDataBackup",JSON.stringify(t)),console.log("Quiz data backup saved to localStorage")}catch(e){console.error("Error saving backup:",e)}}function Jt(){n.closeStatsModal&&n.closeStatsModal.addEventListener("click",Ne),n.closeStatsBtn&&n.closeStatsBtn.addEventListener("click",Ne),n.leaderboardStatsModal&&n.leaderboardStatsModal.addEventListener("click",e=>{e.target===n.leaderboardStatsModal&&Ne()})}async function _n(){if(confirm("Möchtest du ALLE Ranglisten-Daten wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."))try{await bn(),Z(),g("Alle Ranglisten-Daten wurden erfolgreich gelöscht!","success")}catch(e){console.error("Clear error:",e),g("Fehler beim Löschen der Ranglisten-Daten.","error")}}async function Jn(){if(n.leaderboardStatsModal)try{const e=Dt();if(n.statsTotalEntries&&(n.statsTotalEntries.textContent=e.totalEntries),n.statsTotalPlayers&&(n.statsTotalPlayers.textContent=e.totalPlayers),n.statsLastUpdated){const t=e.lastUpdated?new Date(e.lastUpdated).toLocaleString("de-DE"):"Nie";n.statsLastUpdated.textContent=t}n.topPerformersList&&(n.topPerformersList.innerHTML="",e.topPerformers.length===0?n.topPerformersList.innerHTML="<p>Keine Leistungen vorhanden</p>":e.topPerformers.forEach((t,o)=>{const i=document.createElement("div");i.className="performer-item",i.innerHTML=`
                        <span class="rank">${o+1}.</span>
                        <span class="name">${t.name}</span>
                        <span class="score">${t.correctAnswers}/${t.totalQuestions}</span>
                        <span class="accuracy">${t.accuracy.toFixed(1)}%</span>
                        <span class="mode">${t.mode}</span>
                        <span class="category">${t.category}</span>
                    `,n.topPerformersList.appendChild(i)})),n.categoryStats&&(n.categoryStats.innerHTML="",Object.keys(e.categoryStats).forEach(t=>{const o=document.createElement("div");o.className="mode-stats",o.innerHTML=`<h5>${we(t)}</h5>`,Object.keys(e.categoryStats[t]).forEach(i=>{const a=e.categoryStats[t][i],r=document.createElement("div");r.className="category-stat",r.innerHTML=`
                        <span class="category-name">${i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ")}</span>
                        <span class="category-count">${a} Einträge</span>
                    `,o.appendChild(r)}),n.categoryStats.appendChild(o)})),n.leaderboardStatsModal.style.display="flex"}catch(e){console.error("Error showing leaderboard stats:",e),g("Fehler beim Laden der Statistiken.","error")}}function Ne(){n.leaderboardStatsModal&&(n.leaderboardStatsModal.style.display="none")}function we(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}async function Gn(){if(console.log("Starting data migration to unified structure..."),!l)return console.error("Quiz data not available for migration"),!1;l.questions||(l.questions={});const e=new Map;let t=0,o=0;["image-based","time-limited"].forEach(a=>{l[a]&&Object.keys(l[a]).forEach(r=>{r!=="all"&&Array.isArray(l[a][r])&&l[a][r].forEach(c=>{const d=`${c.image||"no-image"}_${c.correctAnswer}_${r}`;e.has(d)?o++:(l.questions[r]||(l.questions[r]=[]),l.questions[r].push(c),e.set(d,!0),t++)})})}),console.log(`Migration completed: ${t} questions migrated, ${o} duplicates removed`);try{return await ln(l),ve(),!0}catch(a){return console.error("Error saving migrated data:",a),!1}}function Zn(){if(!l)return!1;if(!l.questions||Object.keys(l.questions).length===0){const e=["image-based","time-limited"];for(const t of e)if(l[t]&&Object.keys(l[t]).length>0)return!0}return!1}function Wn(){n.contentManagementModal&&(n.contentManagementModal.style.display="flex",Zt(),Wt(),Me(),bt())}function Fe(){n.contentManagementModal&&(n.contentManagementModal.style.display="none")}function Gt(){n.closeContentModal&&n.closeContentModal.addEventListener("click",Fe),n.closeContentBtn&&n.closeContentBtn.addEventListener("click",Fe),n.categoriesTab&&n.categoriesTab.addEventListener("click",Zt),n.questionsTab&&n.questionsTab.addEventListener("click",Xn),n.filterMode&&n.filterMode.addEventListener("change",qt),n.filterCategory&&n.filterCategory.addEventListener("change",qt),n.contentManagementModal&&n.contentManagementModal.addEventListener("click",e=>{e.target===n.contentManagementModal&&Fe()})}function Zt(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.categoriesTab.classList.add("active"),n.questionsTab.classList.remove("active"),n.categoriesContent.classList.add("active"),n.questionsContent.classList.remove("active"))}function Xn(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.questionsTab.classList.add("active"),n.categoriesTab.classList.remove("active"),n.questionsContent.classList.add("active"),n.categoriesContent.classList.remove("active"),bt())}function Wt(){if(!n.categoriesList)return;const e=new Set,t={};if(Object.keys(l).forEach(o=>{Object.keys(l[o]).forEach(i=>{e.add(i),t[i]||(t[i]={modes:[],totalQuestions:0}),t[i].modes.push(o),t[i].totalQuestions+=l[o][i].length})}),n.categoriesList.innerHTML="",e.size===0){n.categoriesList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">📁</div>
                <p>Keine Kategorien vorhanden</p>
            </div>
        `;return}Array.from(e).sort().forEach(o=>{const i=t[o],a=document.createElement("div");a.className="content-item";const r=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," "),c=i.modes.map(d=>we(d)).join(", ");a.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">${r}</div>
                <div class="content-item-meta">
                    <span>Modi: ${c}</span>
                    <span>${i.totalQuestions} Fragen</span>
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteCategory('${o}')">
                    🗑️ Löschen
                </button>
            </div>
        `,n.categoriesList.appendChild(a)})}function Me(){if(!n.questionsList)return;const e=[];Object.keys(l).forEach(t=>{Object.keys(l[t]).forEach(o=>{l[t][o].forEach((i,a)=>{e.push({mode:t,category:o,index:a,question:i,displayName:o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," ")})})})}),Xt(e)}function Xt(e){if(n.questionsList){if(n.questionsList.innerHTML="",e.length===0){n.questionsList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">❓</div>
                <p>Keine Fragen vorhanden</p>
            </div>
        `;return}e.forEach(t=>{const o=document.createElement("div");o.className="content-item";const i=t.question.question||"Bildbasierte Frage",a=!!t.question.image,r=a?`<img src="${t.question.image}" class="question-preview-mini" alt="Vorschau">`:"";o.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">
                    ${r}
                    <span class="question-text-preview">${i}</span>
                </div>
                <div class="content-item-meta">
                    <span>Kategorie: ${t.displayName}</span>
                    <span>Modus: ${we(t.mode)}</span>
                    <span>Antwort: ${t.question.correctAnswer}</span>
                    ${a?"<span>📷 Mit Bild</span>":""}
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-secondary btn-small" onclick="editQuestion('${t.mode}', '${t.category}', ${t.index})" style="margin-right: 0.5rem;">
                    ✏️ Bearbeiten
                </button>
                <button class="btn btn-danger btn-small" onclick="confirmDeleteQuestion('${t.mode}', '${t.category}', ${t.index})">
                    🗑️ Löschen
                </button>
            </div>
        `,n.questionsList.appendChild(o)})}}function bt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value;n.filterMode.innerHTML=`
        <option value="">Alle Modi</option>
        <option value="image-based">Bildbasiert</option>
        <option value="time-limited">Zeitbegrenzt</option>
    `,n.filterMode.value=e;const t=new Set;Object.keys(l).forEach(i=>{Object.keys(l[i]).forEach(a=>{t.add(a)})});const o=n.filterCategory.value;n.filterCategory.innerHTML='<option value="">Alle Kategorien</option>',Array.from(t).sort().forEach(i=>{const a=i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," "),r=document.createElement("option");r.value=i,r.textContent=a,n.filterCategory.appendChild(r)}),n.filterCategory.value=o}function qt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value,t=n.filterCategory.value,o=[];Object.keys(l).forEach(i=>{i!=="questions"&&(e&&i!==e||l[i]&&typeof l[i]=="object"&&Object.keys(l[i]).forEach(a=>{t&&a!==t||Array.isArray(l[i][a])&&l[i][a].forEach((r,c)=>{o.push({mode:i,category:a,index:c,question:r,displayName:a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," ")})})}))}),Xt(o)}let N={isEditing:!1,mode:null,category:null,questionIndex:null,originalQuestion:null};window.editQuestion=function(e,t,o){console.log("=== EDIT QUESTION DEBUG ==="),console.log("Mode:",e),console.log("Category:",t),console.log("Question Index:",o),console.log("Quiz Data Keys:",Object.keys(l));let i=null,a=e;if(e==="questions"?l.questions&&l.questions[t]&&l.questions[t][o]&&(i=l.questions[t][o],i.image&&(a="image-based")):l[e]&&l[e][t]&&l[e][t][o]&&(i=l[e][t][o]),console.log("Found question:",!!i),console.log("Actual mode:",a),console.log("Question data:",i),!i){g("Frage nicht gefunden.","error"),console.error("Question not found at path:",e,t,o);return}N={isEditing:!0,mode:a,category:t,questionIndex:o,originalQuestion:{...i},sourceMode:e},console.log("Edit state set:",N),Rt(),setTimeout(()=>{Yn(i,a,t)},100);const r=document.querySelector("#add-question-modal .modal-header h3");r&&(r.textContent="Frage bearbeiten");const c=n.confirmQuestionBtn;c&&(c.textContent="Änderungen speichern")};function Yn(e,t,o){if(console.log("=== POPULATE EDIT FORM DEBUG ==="),console.log("Question:",e),console.log("Mode:",t),console.log("Category:",o),console.log("Admin elements check:"),console.log("questionModeSelect:",!!n.questionModeSelect),console.log("questionCategorySelect:",!!n.questionCategorySelect),console.log("questionTextInput:",!!n.questionTextInput),console.log("option1Input:",!!n.option1Input),console.log("correctAnswerSelect:",!!n.correctAnswerSelect),n.questionModeSelect?(n.questionModeSelect.value=t,console.log("Mode set to:",t),updateCategoryDropdown(),setTimeout(()=>{n.questionCategorySelect&&(n.questionCategorySelect.value=o,console.log("Category set to:",o),console.log("Available category options:",Array.from(n.questionCategorySelect.options).map(i=>i.value)))},200)):console.error("questionModeSelect element not found!"),n.questionTextInput&&e.question&&(n.questionTextInput.value=e.question,console.log("Question text set to:",e.question)),n.questionImageInput&&e.image&&(n.questionImageInput.value=e.image,console.log("Image URL set to:",e.image),Kt()),e.options&&Array.isArray(e.options)){const i=[n.option1Input,n.option2Input,n.option3Input,n.option4Input];e.options.forEach((a,r)=>{i[r]&&(i[r].value=a,console.log(`Option ${r+1} set to:`,a))}),setTimeout(()=>{Vt(),n.correctAnswerSelect&&e.correctAnswer&&(n.correctAnswerSelect.value=e.correctAnswer,console.log("Correct answer set to:",e.correctAnswer))},150)}n.questionExplanationInput&&e.explanation&&(n.questionExplanationInput.value=e.explanation,console.log("Explanation set to:",e.explanation)),n.questionStreetViewUrlInput&&e.streetViewUrl&&(n.questionStreetViewUrlInput.value=e.streetViewUrl,console.log("Street View URL set to:",e.streetViewUrl)),setTimeout(()=>{_t()},200)}let F={type:null,data:null};function Yt(){n.closeDeleteModal&&n.closeDeleteModal.addEventListener("click",Le),n.cancelDeleteBtn&&n.cancelDeleteBtn.addEventListener("click",Le),n.confirmDeleteBtn&&n.confirmDeleteBtn.addEventListener("click",to),n.deleteConfirmationModal&&n.deleteConfirmationModal.addEventListener("click",e=>{e.target===n.deleteConfirmationModal&&Le()})}function Le(){n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="none",F={type:null,data:null})}window.confirmDeleteCategory=function(e){const t=eo(e);if(F={type:"category",data:{categoryName:e}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Kategorie löschen"),n.deleteModalMessage&&(n.deleteModalMessage.textContent=`Möchtest du die Kategorie "${e}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`),n.deleteModalDetails){const o=e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ");n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Kategorie:</strong> ${o}</li>
                <li><strong>Betroffene Modi:</strong> ${t.modes.map(i=>we(i)).join(", ")}</li>
                <li><strong>Anzahl Fragen:</strong> ${t.totalQuestions}</li>
                <li><strong>Bilder:</strong> ${t.imageCount} Dateien werden gelöscht</li>
                <li><strong>Ordner:</strong> Kategorie-Ordner wird entfernt (falls leer)</li>
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Kategorie löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};window.confirmDeleteQuestion=function(e,t,o){const i=l[e]&&l[e][t]&&l[e][t][o];if(!i){g("Frage nicht gefunden.","error");return}F={type:"question",data:{mode:e,category:t,questionIndex:o}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Frage löschen");const a=i.question||"Bildbasierte Frage";if(n.deleteModalMessage&&(n.deleteModalMessage.textContent="Möchtest du diese Frage wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."),n.deleteModalDetails){const r=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),c=!!i.image;n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Frage:</strong> ${a}</li>
                <li><strong>Kategorie:</strong> ${r}</li>
                <li><strong>Modus:</strong> ${we(e)}</li>
                <li><strong>Richtige Antwort:</strong> ${i.correctAnswer}</li>
                ${c?`<li><strong>Bild:</strong> ${i.image} wird gelöscht</li>`:""}
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Frage löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};function eo(e){const t={modes:[],totalQuestions:0,imageCount:0};return Object.keys(l).forEach(o=>{if(l[o][e]){t.modes.push(o);const i=l[o][e];t.totalQuestions+=i.length,i.forEach(a=>{a.image&&a.image.startsWith("assets/images/")&&t.imageCount++})}}),t}async function to(){if(!(!F.type||!F.data)){n.deleteBtnText&&(n.deleteBtnText.style.display="none"),n.deleteLoading&&(n.deleteLoading.style.display="inline-block"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!0);try{F.type==="category"?await no(F.data.categoryName):F.type==="question"&&await oo(F.data.mode,F.data.category,F.data.questionIndex)}catch(e){console.error("Deletion error:",e),g(`Fehler beim Löschen: ${e.message}`,"error")}finally{n.deleteBtnText&&(n.deleteBtnText.style.display="inline"),n.deleteLoading&&(n.deleteLoading.style.display="none"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!1),Le()}}}async function no(e){try{const t=await fn(e);Object.keys(l).forEach(i=>{l[i][e]&&delete l[i][e]}),l.questions&&l.questions[e]&&(delete l.questions[e],console.log(`Removed category "${e}" from unified structure`)),ve(),await gt(),Z(),Wt(),Me(),bt();const o=t.deletedQuestions>0?` (${t.deletedQuestions} Fragen, ${t.deletedImages.length} Bilder gelöscht)`:"";g(`Kategorie "${e}" erfolgreich gelöscht!${o}`,"success")}catch(t){throw console.error("Error deleting category:",t),new Error(`Kategorie konnte nicht gelöscht werden: ${t.message}`)}}async function oo(e,t,o){try{console.log("=== DELETE QUESTION DEBUG ==="),console.log("Mode:",e),console.log("Category:",t),console.log("Question Index:",o);let i=e;e==="time-limited"&&(i="image-based");const a=l[i]&&l[i][t]&&l[i][t][o];if(console.log("Question to delete:",a),!a)throw new Error("Frage nicht gefunden");const r=await gn(e,t,o);console.log("Backend deletion result:",r),await gt(),console.log("Quiz data refreshed from server"),Z(),Me();const c=r.deletedImagePath?" (Bild gelöscht)":"";g(`Frage erfolgreich gelöscht!${c}`,"success")}catch(i){throw console.error("Error deleting question:",i),new Error(`Frage konnte nicht gelöscht werden: ${i.message}`)}}const y={currentSection:"home",playerName:"Anonym"};let de,R,vt,en,_,Je,Ge,Ee,ee,xe,ye,re,C,x,D,ne,qe,Ze,We,Xe,Ye,J,G,et,tt,nt,ot,it,at,rt,st,lt,ct,dt;function io(){de=document.getElementById("nav-links"),R=document.getElementById("burger"),vt=document.querySelectorAll("main > section"),en=document.querySelectorAll(".nav-links a[data-target]"),document.querySelectorAll(".btn[data-target]"),document.getElementById("player-name-area"),_=document.getElementById("player-name"),Je=document.getElementById("save-player-name"),Ge=document.getElementById("current-player-name"),document.getElementById("learn"),document.getElementById("learn-overview"),Ee=document.getElementById("learn-category-select"),ee=document.getElementById("learn-content-grid"),xe=document.getElementById("sound-toggle"),ye=document.getElementById("theme-select"),re=document.getElementById("time-limit-select"),C=document.getElementById("time-limit-options"),x=document.getElementById("leaderboard-mode-select"),D=document.getElementById("leaderboard-category-select"),ne=document.getElementById("leaderboard-time-select"),qe=document.getElementById("time-limit-filter"),Ze=document.getElementById("refresh-leaderboard"),We=document.getElementById("clear-leaderboard"),Xe=document.getElementById("current-player-display"),Ye=document.getElementById("change-player-btn"),J=document.getElementById("player-name-modal"),G=document.getElementById("new-player-name"),et=document.getElementById("close-player-modal"),tt=document.getElementById("cancel-player-change"),nt=document.getElementById("confirm-player-change"),ot=document.querySelectorAll("#quiz .quiz-option[data-quiz-type]"),dt=document.getElementById("quiz-category-options"),it=document.getElementById("back-to-quiz-selection"),at=document.getElementById("submit-answer"),rt=document.getElementById("next-question"),st=document.getElementById("prev-question"),lt=document.getElementById("quit-quiz"),ct=document.getElementById("finish-quiz")}function tn(e){for(let t=e.length-1;t>0;t--){const o=Math.floor(Math.random()*(t+1));[e[t],e[o]]=[e[o],e[t]]}return e}function g(e,t="info"){const o=document.createElement("div");switch(o.textContent=e,o.style.position="fixed",o.style.bottom="20px",o.style.left="50%",o.style.transform="translateX(-50%)",o.style.padding="10px 20px",o.style.borderRadius="var(--radius)",o.style.color="#fff",o.style.zIndex="2000",o.style.boxShadow="var(--shadow-lg)",o.style.textAlign="center",o.style.opacity="0",o.style.transition="opacity 0.5s ease",t){case"warning":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"error":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"info":default:o.style.backgroundColor="var(--clr-secondary)";break}document.body.appendChild(o),requestAnimationFrame(()=>{o.style.opacity="1"}),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{o.parentNode&&o.remove()},500)},3e3)}function K(e){if(document.getElementById(e)||(console.error(`Section with ID "${e}" not found. Defaulting to home.`),e="home"),y.currentSection=e,vt.forEach(o=>{const i=o.id===e;o.style.display=i?"block":"none"}),en.forEach(o=>{o.classList.toggle("active",o.dataset.target===e)}),de&&R&&de.classList.contains("show")&&(de.classList.remove("show"),R.classList.remove("change"),R.setAttribute("aria-expanded","false")),e!=="quiz-game"&&e!=="quiz-category"&&be(),e!=="quiz"&&C){C.style.display="none";const o=document.getElementById("start-time-limited-quiz"),i=document.getElementById("time-limit-instruction");o&&(o.style.display="none"),i&&(i.style.display="none")}e==="learn"&&(rn("all"),Ee&&(Ee.value="all")),e==="leaderboard"&&ao(),e==="admin"&&Dn(),e==="quiz"&&ut(),window.scrollTo(0,0)}function ao(){!x||!D||(nn(),on(),fe())}function nn(){if(!x||!D)return;const e=x.value;D.innerHTML="";let t=e;if(e==="time-limited"&&(t="image-based"),!l[t])return;const o=Object.keys(l[t]);let i;e==="time-limited"?i=o.filter(a=>["all","landschaft","städte_erkennen","wahrzeichen","geographie_extrem"].includes(a)):i=o,i.forEach(a=>{const r=document.createElement("option");r.value=a,r.textContent=a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," "),D.appendChild(r)})}function on(){if(!qe||!x)return;x.value==="time-limited"?qe.style.display="block":qe.style.display="none"}function fe(){if(!x||!D)return;const e=x.value,t=D.value,o=e==="time-limited"&&ne?parseFloat(ne.value):null;Ft(e,t,o)}function ut(){Xe&&(Xe.textContent=`Aktueller Spieler: ${y.playerName}`)}function ro(){J&&G&&(G.value=y.playerName==="Anonym"?"":y.playerName,J.style.display="flex",G.focus())}function he(){J&&(J.style.display="none")}function Ct(){if(!G)return;const e=G.value.trim();e&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",e),me(),ut(),he(),g(`Spieler geändert zu: ${e}`,"info")):e.length>20?g("Name darf maximal 20 Zeichen haben.","warning"):(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName","Anonym"),me(),ut(),he(),g('Spieler auf "Anonym" zurückgesetzt.',"info"))}function me(){_&&(_.value=y.playerName),Ge&&(Ge.textContent=`Aktueller Spieler: ${y.playerName}`)}function Tt(){if(!_)return;const e=_.value.trim();e&&e.length>0&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",y.playerName),me(),g("Name gespeichert!","info")):(g("Bitte gib einen gültigen Namen ein (1-20 Zeichen).","warning"),_.value=y.playerName)}function an(){if(!y.playerName||y.playerName==="Anonym"){const e=prompt("Bitte gib deinen Spielernamen für die Rangliste ein (max. 20 Zeichen):",y.playerName!=="Anonym"?y.playerName:"");return e&&e.trim()!==""&&e.trim().length<=20?(y.playerName=e.trim(),localStorage.setItem("terraTueftlerPlayerName",y.playerName),me(),!0):e!==null?(alert("Ungültiger Name. Bitte 1-20 Zeichen verwenden."),an()):(localStorage.getItem("terraTueftlerPlayerName")||(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName",y.playerName),me()),!1)}return!0}function so(){if(s.currentQuizType="time-limited",C){C.style.display="block";let e=document.getElementById("time-limit-instruction");if(e)e.style.display="block";else{e=document.createElement("p"),e.id="time-limit-instruction",e.textContent="Wähle dein gewünschtes Zeitlimit pro Frage:",e.style.marginBottom="0.5rem",e.style.fontWeight="600";const o=C.querySelector("select");o?C.insertBefore(e,o):C.appendChild(e)}let t=document.getElementById("start-time-limited-quiz");t?t.style.display="block":(t=document.createElement("button"),t.id="start-time-limited-quiz",t.className="btn",t.textContent="Quiz starten",t.style.marginTop="1rem",t.style.display="block",t.style.width="100%",t.addEventListener("click",()=>{re&&(s.selectedTimeLimit=parseFloat(re.value)||1),C.style.display="none",Te("time-limited")}),C.appendChild(t))}else console.error("Time limit options div not found"),Te("time-limited")}function rn(e="all"){if(!ee){console.warn("Learning content grid not found");return}if(!l){console.error("Quiz data not available for learning content"),ee.innerHTML='<p class="no-content">Lerndaten konnten nicht geladen werden.</p>';return}let t=[];try{if(l.questions)e==="all"?Object.keys(l.questions).forEach(i=>{if(i!=="all"&&l.questions[i]){const a=l.questions[i];Array.isArray(a)&&a.forEach(r=>{r.image&&t.push({...r,category:i,sourceType:"unified"})})}}):l.questions[e]&&Array.isArray(l.questions[e])&&l.questions[e].forEach(i=>{i.image&&t.push({...i,category:e,sourceType:"unified"})});else{const i=["image-based"],a=new Set;e==="all"?i.forEach(r=>{l[r]&&Object.keys(l[r]).forEach(c=>{if(c!=="all"&&l[r][c]){const d=l[r][c];Array.isArray(d)&&d.forEach(m=>{m.image&&!a.has(m.image)&&(a.add(m.image),t.push({...m,category:c,sourceType:r}))})}})}):i.forEach(r=>{l[r]&&l[r][e]&&Array.isArray(l[r][e])&&l[r][e].forEach(c=>{c.image&&!a.has(c.image)&&(a.add(c.image),t.push({...c,category:e,sourceType:r}))})})}}catch(i){console.error("Error processing learning content:",i),ee.innerHTML='<p class="no-content">Fehler beim Laden der Lerninhalte.</p>';return}if(ee.innerHTML="",t.length===0){ee.innerHTML='<p class="no-content">Keine Lerninhalte für diese Kategorie verfügbar.</p>';return}console.log(`Learning content loaded: ${t.length} items from category "${e}"`);const o=t.reduce((i,a)=>(i[a.sourceType]=(i[a.sourceType]||0)+1,i),{});console.log("Content by source:",o),t.sort((i,a)=>i.category!==a.category?i.category.localeCompare(a.category):i.correctAnswer.localeCompare(a.correctAnswer)),t.forEach(i=>{const a=lo(i);ee.appendChild(a)})}function lo(e){const t=document.createElement("div");t.className="learn-reference-item";const o=e.image||"https://placehold.co/400x200/bdc3c7/2c3e50?text=Kein+Bild",i=e.category.charAt(0).toUpperCase()+e.category.slice(1).replace(/_/g," "),a=e.sourceType?`<span class="learn-reference-source" title="Quelle: ${e.sourceType}">${co(e.sourceType)}</span>`:"";return t.innerHTML=`
        <img src="${o}" alt="Lerninhalt" class="learn-reference-image" loading="lazy">
        <div class="learn-reference-content">
            <div class="learn-reference-answer">${e.correctAnswer}</div>
            <div class="learn-reference-explanation">${e.explanation||"Keine zusätzlichen Informationen verfügbar."}</div>
            <div class="learn-reference-meta">
                <div class="learn-reference-category">${i}</div>
                ${a}
            </div>
        </div>
    `,t}function co(e){switch(e){case"image-based":return"Bildrätsel";case"time-limited":return"Zeitlimit";default:return e}}let At=!1;function uo(){if(At){console.log("Event listeners already set up, skipping duplicate setup");return}console.log("Setting up event listeners"),At=!0,R&&de&&R.addEventListener("click",()=>{const e=R.getAttribute("aria-expanded")==="true";R.setAttribute("aria-expanded",String(!e)),de.classList.toggle("show"),R.classList.toggle("change")}),document.querySelectorAll(".nav-links a[data-target], .btn[data-target]").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const o=e.dataset.target;o&&K(o)})}),Je&&_&&(Je.addEventListener("click",Tt),_.addEventListener("keypress",e=>{e.key==="Enter"&&Tt()})),ot&&ot.forEach(e=>{e.addEventListener("click",()=>{const t=e.dataset.quizType;if(t){if(!l||Object.keys(l).length===0){g("Quiz-Daten werden noch geladen. Bitte warten Sie einen Moment.","warning");return}if(an())if(t==="time-limited")so();else{if(C){C.style.display="none";const o=document.getElementById("start-time-limited-quiz"),i=document.getElementById("time-limit-instruction");o&&(o.style.display="none"),i&&(i.style.display="none")}Te(t)}}})}),re&&re.addEventListener("change",e=>{s.selectedTimeLimit=parseFloat(e.target.value)||1}),dt&&dt.addEventListener("click",e=>{const t=e.target.closest(".quiz-option[data-category]");if(t){const o=t.dataset.category;s.currentQuizType&&o&&_e(s.currentQuizType,o)}}),it&&it.addEventListener("click",()=>{if(be(),C){C.style.display="none";const e=document.getElementById("start-time-limited-quiz"),t=document.getElementById("time-limit-instruction");e&&(e.style.display="none"),t&&(t.style.display="none")}K("quiz")}),at&&(console.log("Adding event listener to submit button"),at.addEventListener("click",()=>{console.log("Submit button clicked, calling checkAnswer"),qn(!1)})),rt&&rt.addEventListener("click",Cn),st&&st.addEventListener("click",Tn),ct&&ct.addEventListener("click",Ae),lt&&lt.addEventListener("click",Ae),Ee&&Ee.addEventListener("change",e=>{const t=e.target.value;rn(t)}),x&&x.addEventListener("change",()=>{nn(),on(),fe()}),D&&D.addEventListener("change",()=>{fe()}),ne&&ne.addEventListener("change",()=>{fe()}),Ze&&Ze.addEventListener("click",()=>{fe()}),We&&We.addEventListener("click",()=>{const e=x==null?void 0:x.value,t=(D==null?void 0:D.value)||"all",o=e==="time-limited"&&ne?parseFloat(ne.value):null;Mn(e,t,o)}),Ye&&Ye.addEventListener("click",ro),et&&et.addEventListener("click",he),tt&&tt.addEventListener("click",he),nt&&nt.addEventListener("click",Ct),G&&G.addEventListener("keypress",e=>{e.key==="Enter"&&Ct()}),J&&J.addEventListener("click",e=>{e.target===J&&he()}),xe&&xe.addEventListener("change",e=>{kn(e.target.checked)}),ye&&ye.addEventListener("change",e=>{$t(e.target.value,ye)})}document.addEventListener("DOMContentLoaded",async()=>{console.log("DOM fully loaded and parsed."),await In(),console.log("Data initialized successfully"),io(),ft(),Ot(),y.playerName=localStorage.getItem("terraTueftlerPlayerName")||"Anonym",me(),Sn(ye,xe,uo);const e=window.location.hash.substring(1)||"home",t=Array.from(vt).map(o=>o.id);K(t.includes(e)?e:"home"),re&&(re.value=String(s.selectedTimeLimit)),console.log("TerraTüftler App Initialized.")});
