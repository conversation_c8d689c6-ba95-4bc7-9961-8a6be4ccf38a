(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))a(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const c of r.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&a(c)}).observe(document,{childList:!0,subtree:!0});function o(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function a(i){if(i.ep)return;i.ep=!0;const r=o(i);fetch(i.href,r)}})();const Wt=window.location.origin+"/api";async function J(e,t={}){const o=`${Wt}${e}`,i={...{headers:{"Content-Type":"application/json"}},...t};try{const r=await fetch(o,i);if(!r.ok){const c=await r.json().catch(()=>({error:"Unknown error"}));throw new Error(c.error||`HTTP ${r.status}: ${r.statusText}`)}return await r.json()}catch(r){throw console.error(`API request failed for ${e}:`,r),r}}async function Xt(e){try{const t=await J("/quiz-data",{method:"POST",body:JSON.stringify(e)});return console.log("Quiz data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save quiz data to backend:",t),!1}}async function Yt(e){try{const t=await J("/leaderboard-data",{method:"POST",body:JSON.stringify(e)});return console.log("Leaderboard data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save leaderboard data to backend:",t),!1}}async function en(e,t){try{const o=await J("/add-category",{method:"POST",body:JSON.stringify({categoryName:e,modes:t})});return console.log("Category added to backend:",o.message),!0}catch(o){return console.error("Failed to add category to backend:",o),!1}}async function tn(){try{const e=await J("/quiz-data");return console.log("Quiz data loaded from backend"),e}catch(e){return console.error("Failed to load quiz data from backend:",e),{}}}async function nn(){try{const e=await J("/leaderboard-data");return console.log("Leaderboard data loaded from backend"),e}catch(e){return console.error("Failed to load leaderboard data from backend:",e),{"multiple-choice":{},"image-based":{},"time-limited":{},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}}async function on(e,t,o){try{const a=await J("/delete-question",{method:"DELETE",body:JSON.stringify({mode:e,category:t,questionIndex:o})});return console.log("Question deleted from backend:",a.message),a}catch(a){throw console.error("Failed to delete question from backend:",a),a}}async function an(e){try{const t=await J("/delete-category",{method:"DELETE",body:JSON.stringify({categoryName:e})});return console.log("Category deleted from backend:",t.message),t}catch(t){throw console.error("Failed to delete category from backend:",t),t}}async function ct(){try{if(typeof window<"u"&&window.location.hostname.includes("vercel.app"))return console.log("Detected Vercel deployment, skipping backend check"),!1;const e=new AbortController,t=setTimeout(()=>e.abort(),2e3);return await J("/quiz-data",{signal:e.signal}),clearTimeout(t),!0}catch{return console.warn("Backend API not available, falling back to static file loading"),!1}}let m={};async function bt(){try{if(await ct())try{m=await nn(),console.log("Leaderboard data loaded from backend API");return}catch(a){console.warn("Backend API failed for leaderboard, falling back to static files:",a)}const t=await fetch("/data/leaderboardData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Leaderboard response is not JSON, content-type:",o);const a=await t.text();throw console.error("Response text:",a.substring(0,200)),new Error("Invalid JSON response for leaderboard data")}return m=await t.json(),console.log("Leaderboard data loaded from static file"),await rn(),m}catch(e){return console.error("Failed to load leaderboard data:",e),m=Bt(),m}}function Bt(){return{"image-based":{all:[],landschaft:[],städte_erkennen:[],wahrzeichen:[],geographie_extrem:[],architecture:[],straßenschilder:[]},"time-limited":{all:{"0.1":[],"0.5":[],1:[],2:[],3:[]},landschaft:{"0.1":[],"0.5":[],1:[],2:[],3:[]},städte_erkennen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},wahrzeichen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},geographie_extrem:{"0.1":[],"0.5":[],1:[],2:[],3:[]}},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}function Oe(e,t,o=null){let a=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&o&&(a+=`_${o}s`),a}async function rn(){console.log("Checking for localStorage leaderboard data to migrate...");let e=0;const t=["image-based","time-limited"];for(const o of t)if(m[o])for(const a in m[o])if(o==="time-limited")for(const i in m[o][a]){const r=Oe(o,a,parseFloat(i)),c=vt(r);if(c&&c.length>0){const d=m[o][a][i]||[],u=Fe(d,c);m[o][a][i]=u,e+=c.length}}else{const i=Oe(o,a),r=vt(i);if(r&&r.length>0){const c=m[o][a]||[],d=Fe(c,r);m[o][a]=d,e+=r.length}}e>0&&(console.log(`Migrated ${e} leaderboard entries from localStorage`),await dt())}function vt(e){try{const t=localStorage.getItem(e);return t?JSON.parse(t):[]}catch(t){return console.error("Error reading localStorage leaderboard:",t),[]}}function Fe(e,t){const o=[...e];return t.forEach(a=>{if(!o.some(r=>r.name===a.name&&r.correctAnswers===a.correctAnswers&&r.totalQuestions===a.totalQuestions&&Math.abs(new Date(r.completedAt||r.lastPlayed)-new Date(a.completedAt||a.lastPlayed))<1e3)){const r={name:a.name,correctAnswers:a.correctAnswers,totalQuestions:a.totalQuestions,maxStreak:a.maxStreak||0,completedAt:a.completedAt||a.lastPlayed||new Date().toISOString(),mode:a.mode,category:a.category,timeLimit:a.timeLimit};o.push(r)}}),o.sort((a,i)=>{if(i.correctAnswers!==a.correctAnswers)return i.correctAnswers-a.correctAnswers;const r=a.totalQuestions>0?a.correctAnswers/a.totalQuestions:0,c=i.totalQuestions>0?i.correctAnswers/i.totalQuestions:0;return c!==r?c-r:new Date(i.completedAt||i.lastPlayed)-new Date(a.completedAt||a.lastPlayed)}),o.slice(0,50)}function Lt(e,t="all",o=null){return m[e]?e==="time-limited"?!m[e][t]||!m[e][t][o]?[]:m[e][t][o]||[]:m[e][t]||[]:[]}async function sn(e,t,o,a,i,r,c=null){if(!e||!o||!a||t<0)return!1;const u={name:e||"Anonym",correctAnswers:t,totalQuestions:i,maxStreak:r,completedAt:new Date().toISOString(),mode:o,category:a,timeLimit:c},f=Lt(o,a,c);f.push(u);const E=Fe([],f);return o==="time-limited"?(m[o][a]||(m[o][a]={}),m[o][a][c]=E):(m[o]||(m[o]={}),m[o][a]=E),ln(),await dt(),cn(o,a,c,E),typeof window<"u"&&window.updateLeaderboardDisplay&&window.updateLeaderboardDisplay(),!0}function ln(){let e=0;Object.keys(m).forEach(t=>{t!=="_metadata"&&(t==="time-limited"?Object.keys(m[t]).forEach(o=>{Object.keys(m[t][o]).forEach(a=>{e+=m[t][o][a].length})}):Object.keys(m[t]).forEach(o=>{e+=m[t][o].length}))}),m._metadata={...m._metadata,lastUpdated:new Date().toISOString(),totalEntries:e}}function cn(e,t,o,a){try{const i=Oe(e,t,o);localStorage.setItem(i,JSON.stringify(a))}catch(i){console.error("Error saving to localStorage:",i)}}async function dt(){try{if(await ct()&&await Yt(m))return console.log("Leaderboard data saved to backend"),!0;const t=JSON.stringify(m,null,2);return localStorage.setItem("terraTueftlerLeaderboardPersistent",t),console.log("Leaderboard data saved to localStorage (fallback)"),!0}catch(e){return console.error("Error saving leaderboard data:",e),!1}}async function dn(){try{const e=localStorage.getItem("terraTueftlerLeaderboardPersistent");e?(m=JSON.parse(e),console.log("Loaded leaderboard data from persistent storage")):await bt()}catch(e){console.error("Error loading persistent leaderboard data:",e),await bt()}return m}async function un(){const e=JSON.parse(JSON.stringify(m));return localStorage.setItem("terraTueftlerLeaderboardBackup",JSON.stringify({timestamp:new Date().toISOString(),data:e})),m=Bt(),Object.keys(localStorage).forEach(t=>{t.startsWith("terraTueftlerLeaderboard_")&&localStorage.removeItem(t)}),await dt(),!0}function qt(){var i;let e=0,t=new Set,o=[],a={};return Object.keys(m).forEach(r=>{r!=="_metadata"&&(a[r]={},r==="time-limited"?Object.keys(m[r]).forEach(c=>{a[r][c]=0,Object.keys(m[r][c]).forEach(d=>{const u=m[r][c][d];e+=u.length,a[r][c]+=u.length,u.forEach(f=>{t.add(f.name),o.push({...f,accuracy:f.totalQuestions>0?f.correctAnswers/f.totalQuestions*100:0})})})}):Object.keys(m[r]).forEach(c=>{const d=m[r][c];e+=d.length,a[r][c]=d.length,d.forEach(u=>{t.add(u.name),o.push({...u,accuracy:u.totalQuestions>0?u.correctAnswers/u.totalQuestions*100:0})})}))}),o.sort((r,c)=>c.correctAnswers!==r.correctAnswers?c.correctAnswers-r.correctAnswers:c.accuracy-r.accuracy),{totalEntries:e,totalPlayers:t.size,topPerformers:o.slice(0,10),categoryStats:a,lastUpdated:(i=m._metadata)==null?void 0:i.lastUpdated}}let l={},Pe={};async function Ct(){try{if(await ct())try{l=await tn(),console.log("Quiz data loaded from backend API");return}catch(a){console.warn("Backend API failed, falling back to static files:",a)}const t=await fetch("/data/quizData.json");if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);const o=t.headers.get("content-type");if(!o||!o.includes("application/json")){console.warn("Response is not JSON, content-type:",o);const a=await t.text();throw console.error("Response text:",a.substring(0,200)),new Error("Invalid JSON response")}l=await t.json(),console.log("Quiz data loaded from static file")}catch(e){console.error("Failed to load quiz data:",e),l={"time-limited":{},"image-based":{},all:[]},console.warn("Using fallback empty quiz data structure")}}async function mn(){try{const e=await fetch("/data/learningData.json");if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=e.headers.get("content-type");if(!t||!t.includes("application/json")){console.warn("Learning data response is not JSON, content-type:",t);const o=await e.text();throw console.error("Response text:",o.substring(0,200)),new Error("Invalid JSON response for learning data")}Pe=await e.json(),console.log("Learning data loaded successfully")}catch(e){console.error("Failed to load learning data:",e),Pe={},console.warn("Using fallback empty learning data structure")}}async function gn(){return await Promise.all([Ct(),mn(),dn()]),{quizData:l,learningData:Pe}}async function Tt(){return console.log("🔄 Refreshing quiz data after admin changes..."),await Ct(),console.log("✅ Quiz data refreshed successfully"),l}const R={soundEnabled:!0,currentTheme:"theme-standard"};function At(e,t){["theme-standard","theme-dark"].forEach(i=>document.body.classList.remove(i));const a=e||"theme-standard";document.body.classList.add(a),R.currentTheme=a,localStorage.setItem("terraTueftlerTheme",a),t&&(t.value=a)}function fn(e,t,o){const a=localStorage.getItem("terraTueftlerTheme")||"theme-standard";At(a,e);const i=localStorage.getItem("terraTueftlerSoundEnabled");R.soundEnabled=i!==null?JSON.parse(i):!0,t&&(t.checked=R.soundEnabled),i===null&&localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled)),typeof o=="function"&&o()}function pn(e){R.soundEnabled=e,localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled))}function Ue(e){if(!(!R.soundEnabled||!window.AudioContext&&!window.webkitAudioContext))try{const t=new(window.AudioContext||window.webkitAudioContext);let o,a;switch(a=t.createGain(),a.connect(t.destination),a.gain.setValueAtTime(.5,t.currentTime),o=t.createOscillator(),o.connect(a),e){case"correct":o.type="sine",o.frequency.setValueAtTime(523.25,t.currentTime),a.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),o.start(t.currentTime),o.stop(t.currentTime+.3);break;case"incorrect":o.type="square",o.frequency.setValueAtTime(110,t.currentTime),a.gain.exponentialRampToValueAtTime(.001,t.currentTime+.4),o.start(t.currentTime),o.stop(t.currentTime+.4);break;case"quizEnd":const i=[261.63,329.63,391.99,523.25];let r=t.currentTime;i.forEach((c,d)=>{const u=t.createOscillator(),f=t.createGain();u.connect(f),f.connect(t.destination),u.type="triangle",u.frequency.setValueAtTime(c,r+d*.15),f.gain.setValueAtTime(.4,r+d*.15),f.gain.exponentialRampToValueAtTime(.001,r+d*.15+.1),u.start(r+d*.15),u.stop(r+d*.15+.1)});break}setTimeout(()=>{t.state!=="closed"&&t.close().catch(i=>console.warn("Error closing AudioContext:",i))},1e3)}catch(t){console.warn("AudioContext could not be started or used.",t),R.soundEnabled=!1;const o=document.getElementById("sound-toggle");o&&(o.checked=!1)}}console.log("🔧 QUIZ.JS LOADED - Version with question text fix - 2024-fix");const s={currentQuizType:"",currentCategory:"",currentQuestions:[],currentQuestionIndex:0,selectedAnswer:null,userAnswers:[],questionStates:[],score:0,currentStreak:0,maxStreak:0,quizEnded:!1,selectedTimeLimit:1,timerId:null,isTimerRunning:!1,imagePhaseActive:!1};let ze,je,ae,b,B,p,I,h,T,Ce,me,le,v,C,k,P,x,Re,He,Ke,ee,te,ie;function ut(){ze=document.getElementById("quiz"),ze&&ze.querySelectorAll(".quiz-option[data-quiz-type]"),document.getElementById("time-limit-options"),document.getElementById("time-limit-select"),document.getElementById("quiz-category"),je=document.querySelector("#quiz-category h2"),ae=document.getElementById("quiz-category-options"),document.getElementById("back-to-quiz-selection"),b=document.getElementById("quiz-game"),B=document.getElementById("question-text"),p=document.getElementById("question-image"),I=document.getElementById("answer-options"),h=document.getElementById("feedback"),T=document.getElementById("timer"),Ce=document.getElementById("timer-phase"),me=document.getElementById("time-left"),le=document.getElementById("prev-question"),v=document.getElementById("submit-answer"),C=document.getElementById("next-question"),k=document.getElementById("quit-quiz"),P=document.getElementById("finish-quiz"),x=document.getElementById("scoreboard"),Re=document.getElementById("score"),He=document.getElementById("final-streak"),Ke=document.getElementById("score-message"),ee=document.getElementById("streak-display"),te=document.getElementById("current-streak"),document.getElementById("leaderboard"),document.getElementById("leaderboard-mode-select"),ie=document.getElementById("leaderboard-list"),document.getElementById("clear-leaderboard")}function Ee(){clearInterval(s.timerId),s.currentQuizType="",s.currentCategory="",s.currentQuestions=[],s.currentQuestionIndex=0,s.selectedAnswer=null,s.userAnswers=[],s.questionStates=[],s.score=0,s.currentStreak=0,s.maxStreak=0,s.quizEnded=!1,s.timerId=null,s.isTimerRunning=!1,s.imagePhaseActive=!1,T&&(T.style.display="none"),h&&(h.style.display="none"),x&&(x.style.display="none"),ee&&(ee.style.display="none"),v&&(v.style.display="inline-block"),C&&(C.style.display="none"),le&&(le.style.display="none"),P&&(P.style.display="none"),k&&(k.style.display="none"),B&&(B.textContent="Frage wird geladen..."),p&&(p.style.display="none"),I&&(I.innerHTML="");const e=b?b.querySelector(".question-container"):null,t=b?b.querySelector(".quiz-controls"):null;e&&(e.style.display="block"),t&&(t.style.display="flex")}function Te(e){if(!ae){console.error("Quiz category options container not found.");return}s.currentQuizType=e,ae.innerHTML="";let t=Mt(e);e==="time-limited"&&(t+=` (${s.selectedTimeLimit}s)`),je&&(je.textContent=`Wähle eine Kategorie (${t})`);let o=[];if(l.questions)o=Object.keys(l.questions);else{let a=e;if(e==="time-limited"&&(a="image-based"),!l[a]){console.error(`Quiz data source "${a}" not found in quizData.`),ae.innerHTML="<p>Fehler: Quiztyp nicht gefunden.</p>",j("quiz-category");return}o=Object.keys(l[a])}o.length===0?ae.innerHTML="<p>Keine Kategorien für diesen Modus verfügbar.</p>":o.forEach(a=>{const i=document.createElement("div");i.classList.add("quiz-option");const r=a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," ");i.textContent=r,i.dataset.category=a,ae.appendChild(i)}),j("quiz-category")}function Ve(e,t){var r;Ee(),s.currentQuizType=e,s.currentCategory=t;let o=[];if(l.questions&&l.questions[t])o=l.questions[t];else{let c=e;if(e==="time-limited"&&(c="image-based"),!((r=l[c])!=null&&r[t])||l[c][t].length===0){alert("Fehler: Quiz konnte nicht geladen werden. Kategorie oder Typ ungültig."),j("quiz-category");return}o=l[c][t]}if(o.length===0){alert("Fehler: Keine Fragen in dieser Kategorie verfügbar."),j("quiz-category");return}s.currentQuestions=Kt([...o]),s.userAnswers=new Array(s.currentQuestions.length).fill(null),s.questionStates=s.currentQuestions.map(()=>({answered:!1,locked:!1})),b||ut();const a=b==null?void 0:b.querySelector(".question-container"),i=b==null?void 0:b.querySelector(".quiz-controls");a&&(a.style.display="block"),i&&(i.style.display="flex"),B&&(B.style.display="block"),I&&(I.style.display="grid"),x&&(x.style.display="none"),ee&&(ee.style.display="block"),te&&(te.textContent=s.currentStreak),k&&(k.style.display="inline-block"),j("quiz-game"),mt(),e==="time-limited"&&T&&me&&Ce?gt():T&&(T.style.display="none")}function mt(){if(!B||!I||!h||!v||!C||!te||!k){console.error("Required quiz game elements not found for loading question.");return}if(s.quizEnded||s.currentQuestionIndex>=s.currentQuestions.length){Ae();return}const e=s.currentQuestions[s.currentQuestionIndex],t=s.questionStates[s.currentQuestionIndex];if(s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],console.log("DEBUG: Current question:",e),console.log("DEBUG: Question field exists:",!!e.question),console.log("DEBUG: Quiz type:",s.currentQuizType),console.log("DEBUG: questionTextElement exists:",!!B),!B&&(console.error("ERROR: questionTextElement is null! Re-caching DOM elements..."),ut(),!B)){console.error("ERROR: questionTextElement still null after re-caching!");return}if(s.currentQuizType==="time-limited")B.style.display="none",console.log("DEBUG: Time-limited quiz - hiding question text");else{if(B.style.display="block",e.question){const i=`Frage ${s.currentQuestionIndex+1}: ${e.question}`;B.textContent=i,console.log("DEBUG: Using original question text:",i)}else{const i=`Frage ${s.currentQuestionIndex+1}: In welchem Land befindet sich das?`;B.textContent=i,console.log("DEBUG: Using fallback question text:",i)}console.log("DEBUG: Final question element text:",B.textContent),console.log("DEBUG: Final question element innerHTML:",B.innerHTML)}I.innerHTML="",h.style.display="none",h.textContent="",te.textContent=s.currentStreak;const o=t.locked;if(o){if(v.style.display="none",C.style.display="inline-block",k.style.display="inline-block",I.style.pointerEvents="none",t.answered){h.style.display="block";const r=s.userAnswers[s.currentQuestionIndex]===e.correctAnswer;h.textContent=r?"✓ Richtig! Diese Antwort ist gesperrt.":"✗ Falsch. Diese Antwort ist gesperrt.",h.className=r?"feedback correct":"feedback incorrect"}}else v.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I.style.pointerEvents="auto";if(p&&e.image)if(p.src=e.image,p.style.display="block",e.question?p.alt=`Bild zur Frage: ${e.question}`:p.alt=`Bild zur Frage ${s.currentQuestionIndex+1}`,e.streetViewUrl){p.classList.remove("streetview-locked","streetview-unlocked","streetview-unlocking"),p.classList.add("streetview-available"),p.removeEventListener("click",Ie),p.removeEventListener("click",ke);const i=s.questionStates[s.currentQuestionIndex];i&&i.locked?(p.classList.add("streetview-unlocked"),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",Ie)):(p.classList.add("streetview-locked"),p.style.cursor="not-allowed",p.title="Beantworte die Frage, um Street View zu entsperren",p.addEventListener("click",ke))}else p.classList.remove("streetview-available","streetview-locked","streetview-unlocked","streetview-unlocking"),p.style.cursor="default",p.title="",p.removeEventListener("click",Ie),p.removeEventListener("click",ke);else p&&(p.style.display="none");Kt([...e.options]).forEach(i=>{const r=document.createElement("div");r.classList.add("answer-option"),r.textContent=i,r.dataset.option=i,o?(r.classList.add("locked"),i===e.correctAnswer?r.classList.add("correct"):i===s.selectedAnswer&&r.classList.add("incorrect"),i===s.selectedAnswer&&r.classList.add("selected")):(i===s.selectedAnswer&&r.classList.add("selected"),r.addEventListener("click",yn)),I.appendChild(r)}),xt()}function yn(e){if(!s.questionStates[s.currentQuestionIndex].locked&&v&&v.style.display!=="none"){const o=e.target.dataset.option;s.selectedAnswer=o,s.userAnswers[s.currentQuestionIndex]=o,document.querySelectorAll("#answer-options .answer-option").forEach(a=>{a.classList.remove("selected")}),e.target.classList.add("selected")}}function Ie(e){e.preventDefault();const t=s.currentQuestions[s.currentQuestionIndex];t&&t.streetViewUrl&&(window.open(t.streetViewUrl,"_blank","noopener,noreferrer"),g("Street View-Location wird geöffnet...","info"))}function ke(e){e.preventDefault(),g("Beantworte zuerst die Frage, um Street View zu entsperren!","warning")}function hn(){if(!p)return;const e=s.currentQuestions[s.currentQuestionIndex];e&&e.streetViewUrl&&p.classList.contains("streetview-locked")&&(p.classList.remove("streetview-locked"),p.classList.add("streetview-unlocking"),p.removeEventListener("click",ke),p.style.cursor="pointer",p.title="Klicke hier, um die Street View-Location zu besuchen!",p.addEventListener("click",Ie),setTimeout(()=>{p.classList.remove("streetview-unlocking"),p.classList.add("streetview-unlocked")},600))}function xt(){if(!le||!P||!k||!v||!C)return;le.style.display=s.currentQuestionIndex>0?"inline-block":"none",k&&(k.style.display="inline-block"),v.style.display==="none"?(C.style.display=s.currentQuestionIndex<s.currentQuestions.length-1?"inline-block":"none",P.style.display=s.currentQuestionIndex>=s.currentQuestions.length-1?"inline-block":"none"):(C.style.display="none",P.style.display="none")}function En(e=!1){if(console.log("checkAnswer called with isTimeout:",e,"current score:",s.score,"current streak:",s.currentStreak),v&&v.style.display==="none"&&!e){console.log("Answer already submitted for this question, ignoring duplicate call");return}if(!v||!h||!I||!C||!P||!te||!k){console.error("Required quiz elements not found for checking answer.");return}if(!e&&!s.selectedAnswer){g("Bitte wähle zuerst eine Antwort aus.","warning");return}clearInterval(s.timerId),s.isTimerRunning=!1,v.style.display="none",I.style.pointerEvents="none";const t=s.currentQuestions[s.currentQuestionIndex],o=!e&&s.selectedAnswer===t.correctAnswer;hn(),o?(s.quizEnded||(console.log("Score before increment:",s.score),s.score++,console.log("Score after increment:",s.score),console.log("Streak before increment:",s.currentStreak),s.currentStreak++,console.log("Streak after increment:",s.currentStreak),s.maxStreak=Math.max(s.maxStreak,s.currentStreak),console.log("Max streak updated to:",s.maxStreak)),h.textContent=`Richtig! ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-correct)",Ue("correct")):(e?h.textContent=`Zeit abgelaufen! Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`:h.textContent=`Falsch. Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`,h.style.backgroundColor="var(--clr-feedback-incorrect)",Ue("incorrect")),te.textContent=s.currentStreak,document.querySelectorAll("#answer-options .answer-option").forEach(a=>{a.classList.remove("selected"),a.dataset.option===t.correctAnswer?a.classList.add("correct"):a.dataset.option===s.selectedAnswer&&!o&&a.classList.add("incorrect")}),h.style.display="block",s.questionStates[s.currentQuestionIndex].answered=!0,xt()}function bn(){!C||!v||!h||!k||(s.questionStates[s.currentQuestionIndex]&&(s.questionStates[s.currentQuestionIndex].locked=!0),s.currentQuestionIndex<s.currentQuestions.length-1?(s.currentQuestionIndex++,s.selectedAnswer=null,h.style.display="none",v.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),mt(),s.currentQuizType==="time-limited"&&gt()):Ae())}function vn(){clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,!(!le||!v||!C||!P||!h||!k)&&s.currentQuestionIndex>0&&(s.currentQuestionIndex--,s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],h.style.display="none",v.style.display="inline-block",C.style.display="none",P.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),mt(),s.currentQuizType==="time-limited"&&gt())}async function Ae(){if(console.log("finishQuiz called. Current quiz state:",JSON.stringify(s)),clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,s.quizEnded=!0,y.playerName&&y.playerName!=="Anonym"){const o=s.currentQuizType==="time-limited"?s.selectedTimeLimit:null;await sn(y.playerName,s.score,s.currentQuizType,s.currentCategory,s.currentQuestions.length,s.maxStreak,o)}const e=s.currentQuestionIndex<s.currentQuestions.length,t=s.currentQuizType;s.currentCategory,e&&(!x||x.style.display==="none")?(console.log("Finishing mid-quiz, going to category selection."),Ee(),Te(t||"image-based")):(console.log("Quiz ended naturally or from score screen, showing score."),wn())}function wn(){if(!x||!Re||!He||!Ke){console.error("Scoreboard elements not found.");return}const e=b==null?void 0:b.querySelector(".question-container"),t=b==null?void 0:b.querySelector(".quiz-controls");e&&(e.style.display="none"),T&&(T.style.display="none"),h&&(h.style.display="none"),t&&(t.style.display="none"),ee&&(ee.style.display="none"),Re.textContent=`${s.score} / ${s.currentQuestions.length}`,He.textContent=s.maxStreak;let o="";const a=s.currentQuestions.length>0?s.score/s.currentQuestions.length:0;if(s.maxStreak>=10?o=`Wow, ${s.maxStreak} in Folge! Spitzenleistung!`:s.maxStreak>=5?o=`Starke Serie von ${s.maxStreak}! Gut gemacht!`:a>=.7?o="Sehr gutes Ergebnis!":a>=.5?o="Gut gemacht!":o="Übung macht den Meister! Schau doch mal im Lernbereich vorbei.",Ke.textContent=o,x.style.display="block",Ue("quizEnd"),x){let i=x.querySelector(".scoreboard-actions");i||(i=document.createElement("div"),i.className="scoreboard-actions",x.appendChild(i)),i.innerHTML="";const r=document.createElement("button");r.textContent="Erneut spielen (Zufall)",r.className="btn",r.id="play-again-random-btn",r.addEventListener("click",()=>{Ve(s.currentQuizType,s.currentCategory)}),i.appendChild(r);const c=document.createElement("button");c.textContent="Fortfahren",c.className="btn",c.id="fortfahren-btn",c.addEventListener("click",()=>{Ve(s.currentQuizType,s.currentCategory)}),i.appendChild(c);const d=document.createElement("button");d.textContent="Quiz beenden",d.className="btn",d.id="scoreboard-main-menu-btn",d.addEventListener("click",()=>{Ee(),j("home")}),i.appendChild(d)}}function gt(){if(!T||!me||!Ce)return;s.imagePhaseActive=!0,T.style.display="block",T.style.background="var(--clr-primary)",Ce.textContent="Bild sichtbar:";let e=s.selectedTimeLimit;me.textContent=e.toFixed(1),clearInterval(s.timerId),s.isTimerRunning=!0,p&&(p.style.display="block");const t=e<1?100:1e3,o=e<1?.1:1;s.timerId=setInterval(()=>{e-=o,e<=0?(e=0,clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,p&&(p.style.display="none",g("Bild ausgeblendet - Wähle das Land!","info")),T&&(T.style.display="none")):me.textContent=e<1?e.toFixed(1):Math.ceil(e)},t)}function In(e,t){if(l.questions&&l.questions[t])return l.questions[t].length;{let o=e;return e==="time-limited"&&(o="image-based"),!l[o]||!l[o][t]?0:l[o][t].length}}function kn(e,t="all",o=null){if(!ie||!e){ie||console.error("Leaderboard list element not found.");return}const a=Lt(e,t,o),i=In(e,t);if(ie.innerHTML="",a.length===0){const r=Mt(e),c=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),d=o?` (${o}s)`:"";ie.innerHTML=`<li style="text-align: center; color: var(--clr-text-light);">Noch keine Einträge für "${r} - ${c}${d}" vorhanden. Spiel ein Quiz!</li>`;return}a.forEach((r,c)=>{const d=document.createElement("li");d.classList.add("leaderboard-item");const u=i>0&&r.correctAnswers>=i;u&&d.classList.add("perfect-score");const f=document.createElement("span");f.classList.add("leaderboard-rank"),f.textContent=`${c+1}.`;const E=document.createElement("span");E.classList.add("leaderboard-name");const z=u?"⭐ ":"";E.textContent=z+r.name;const A=document.createElement("span");A.classList.add("leaderboard-score");const N=r.totalQuestions>0?Math.round(r.correctAnswers/r.totalQuestions*100):0;A.textContent=`${r.correctAnswers}/${r.totalQuestions} (${N}%)`;const O=document.createElement("span");O.classList.add("leaderboard-streak"),r.maxStreak!==void 0&&(O.textContent=`Serie: ${r.maxStreak}`);const M=document.createElement("span");if(M.classList.add("leaderboard-time"),r.completedAt){const Q=new Date(r.completedAt),w=Q.toLocaleDateString("de-DE")+" "+Q.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});M.textContent=w}else if(r.lastPlayed){const Q=new Date(r.lastPlayed),w=Q.toLocaleDateString("de-DE")+" "+Q.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});M.textContent=w}d.appendChild(f),d.appendChild(E),d.appendChild(A),r.maxStreak!==void 0&&d.appendChild(O),M.textContent&&d.appendChild(M),ie.appendChild(d)})}function Mt(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}const S={isInitialized:!1,dragDropInitialized:!1,currentStats:{totalCategories:0,totalQuestions:0,totalModes:2}};let n={},Y=!1;function Qt(){n={adminSection:document.getElementById("admin"),addCategoryBtn:document.getElementById("add-category-btn"),addQuestionBtn:document.getElementById("add-question-btn"),manageContentBtn:document.getElementById("manage-content-btn"),exportDataBtn:document.getElementById("export-data-btn"),importDataBtn:document.getElementById("import-data-btn"),totalCategoriesSpan:document.getElementById("total-categories"),totalQuestionsSpan:document.getElementById("total-questions"),totalModesSpan:document.getElementById("total-modes"),totalLeaderboardEntriesSpan:document.getElementById("total-leaderboard-entries"),totalPlayersSpan:document.getElementById("total-players"),clearLeaderboardBtn:document.getElementById("clear-leaderboard-btn"),leaderboardStatsBtn:document.getElementById("leaderboard-stats-btn"),leaderboardStatsModal:document.getElementById("leaderboard-stats-modal"),closeStatsModal:document.getElementById("close-stats-modal"),closeStatsBtn:document.getElementById("close-stats"),statsTotalEntries:document.getElementById("stats-total-entries"),statsTotalPlayers:document.getElementById("stats-total-players"),statsLastUpdated:document.getElementById("stats-last-updated"),topPerformersList:document.getElementById("top-performers-list"),categoryStats:document.getElementById("category-stats"),addCategoryModal:document.getElementById("add-category-modal"),closeCategoryModal:document.getElementById("close-category-modal"),categoryNameInput:document.getElementById("category-name"),modeImageBased:document.getElementById("mode-image-based"),modeTimeLimited:document.getElementById("mode-time-limited"),categoryDescriptionInput:document.getElementById("category-description"),cancelCategoryBtn:document.getElementById("cancel-category"),confirmCategoryBtn:document.getElementById("confirm-category"),addQuestionModal:document.getElementById("add-question-modal"),closeQuestionModal:document.getElementById("close-question-modal"),questionCategorySelect:document.getElementById("question-category"),questionTextInput:document.getElementById("admin-question-text"),questionImageInput:document.getElementById("question-image"),questionImageFileInput:document.getElementById("question-image-file"),imagePreview:document.getElementById("image-preview"),previewImg:document.getElementById("preview-img"),imageFilename:document.getElementById("image-filename"),imageSize:document.getElementById("image-size"),option1Input:document.getElementById("option-1"),option2Input:document.getElementById("option-2"),option3Input:document.getElementById("option-3"),option4Input:document.getElementById("option-4"),correctAnswerSelect:document.getElementById("correct-answer"),questionExplanationInput:document.getElementById("question-explanation"),questionStreetViewUrlInput:document.getElementById("question-streetview-url"),questionPreview:document.getElementById("question-preview"),previewQuestionBtn:document.getElementById("preview-question"),cancelQuestionBtn:document.getElementById("cancel-question"),confirmQuestionBtn:document.getElementById("confirm-question"),contentManagementModal:document.getElementById("content-management-modal"),closeContentModal:document.getElementById("close-content-modal"),categoriesTab:document.getElementById("categories-tab"),questionsTab:document.getElementById("questions-tab"),categoriesContent:document.getElementById("categories-content"),questionsContent:document.getElementById("questions-content"),categoriesList:document.getElementById("categories-list"),questionsList:document.getElementById("questions-list"),filterMode:document.getElementById("filter-mode"),filterCategory:document.getElementById("filter-category"),closeContentBtn:document.getElementById("close-content"),deleteConfirmationModal:document.getElementById("delete-confirmation-modal"),closeDeleteModal:document.getElementById("close-delete-modal"),deleteModalTitle:document.getElementById("delete-modal-title"),deleteModalMessage:document.getElementById("delete-modal-message"),deleteModalDetails:document.getElementById("delete-modal-details"),cancelDeleteBtn:document.getElementById("cancel-delete"),confirmDeleteBtn:document.getElementById("confirm-delete"),deleteBtnText:document.getElementById("delete-btn-text"),deleteLoading:document.getElementById("delete-loading")}}function Sn(){S.isInitialized||(console.log("🔧 Initializing admin interface..."),Qt(),console.log("=== ADMIN ELEMENTS DEBUG ==="),Object.keys(n).forEach(e=>{console.log(`${e}:`,n[e]?"✅ Found":"❌ Not found")}),Bn(),Dt(),zt(),Ot(),Ft(),Rt(),Hn()&&(console.log("Data migration needed - migrating to unified structure..."),Rn().then(e=>{e?(console.log("Data migration completed successfully"),g("Datenstruktur wurde automatisch aktualisiert und optimiert!","success")):(console.error("Data migration failed"),g("Warnung: Datenstruktur-Update fehlgeschlagen","warning")),_()})),_(),S.isInitialized=!0,console.log("Admin interface initialized"))}function Bn(){n.addCategoryBtn&&n.addCategoryBtn.addEventListener("click",Ln),n.addQuestionBtn&&n.addQuestionBtn.addEventListener("click",qn),n.manageContentBtn&&n.manageContentBtn.addEventListener("click",Kn),n.exportDataBtn&&n.exportDataBtn.addEventListener("click",On),n.importDataBtn&&n.importDataBtn.addEventListener("click",Fn),n.clearLeaderboardBtn&&n.clearLeaderboardBtn.addEventListener("click",Un),n.leaderboardStatsBtn&&n.leaderboardStatsBtn.addEventListener("click",jn),Ot(),Dt(),zt(),Ft(),Rt()}function Dt(){n.closeCategoryModal&&n.closeCategoryModal.addEventListener("click",Se),n.cancelCategoryBtn&&n.cancelCategoryBtn.addEventListener("click",Se),n.confirmCategoryBtn&&n.confirmCategoryBtn.addEventListener("click",Dn),n.addCategoryModal&&n.addCategoryModal.addEventListener("click",e=>{e.target===n.addCategoryModal&&Se()})}function zt(){n.closeQuestionModal&&n.closeQuestionModal.addEventListener("click",Be),n.cancelQuestionBtn&&n.cancelQuestionBtn.addEventListener("click",Be),n.confirmQuestionBtn&&n.confirmQuestionBtn.addEventListener("click",$n),n.previewQuestionBtn&&n.previewQuestionBtn.addEventListener("click",Nn),n.questionImageInput&&n.questionImageInput.addEventListener("input",An),n.questionImageFileInput&&n.questionImageFileInput.addEventListener("change",Tn),[n.option1Input,n.option2Input,n.option3Input,n.option4Input].forEach(t=>{t&&t.addEventListener("input",Qn)}),n.addQuestionModal&&n.addQuestionModal.addEventListener("click",t=>{t.target===n.addQuestionModal&&Be()})}function _(){let e=0,t=0;Object.keys(l).forEach(a=>{const i=Object.keys(l[a]);e+=i.length,i.forEach(r=>{t+=l[a][r].length})});const o=new Set;Object.keys(l).forEach(a=>{Object.keys(l[a]).forEach(i=>{o.add(i)})}),S.currentStats.totalCategories=o.size,S.currentStats.totalQuestions=t;try{const a=qt();S.currentStats.totalLeaderboardEntries=a.totalEntries,S.currentStats.totalPlayers=a.totalPlayers}catch(a){console.error("Error getting leaderboard stats:",a),S.currentStats.totalLeaderboardEntries=0,S.currentStats.totalPlayers=0}n.totalCategoriesSpan&&(n.totalCategoriesSpan.textContent=S.currentStats.totalCategories),n.totalQuestionsSpan&&(n.totalQuestionsSpan.textContent=S.currentStats.totalQuestions),n.totalModesSpan&&(n.totalModesSpan.textContent=S.currentStats.totalModes),n.totalLeaderboardEntriesSpan&&(n.totalLeaderboardEntriesSpan.textContent=S.currentStats.totalLeaderboardEntries),n.totalPlayersSpan&&(n.totalPlayersSpan.textContent=S.currentStats.totalPlayers)}function Ln(){n.addCategoryModal&&($t(),n.addCategoryModal.style.display="flex",n.categoryNameInput&&n.categoryNameInput.focus())}function Se(){n.addCategoryModal&&(n.addCategoryModal.style.display="none",$t())}function $t(){n.categoryNameInput&&(n.categoryNameInput.value=""),n.categoryDescriptionInput&&(n.categoryDescriptionInput.value=""),n.modeImageBased&&(n.modeImageBased.checked=!1),n.modeTimeLimited&&(n.modeTimeLimited.checked=!1)}function qn(){n.addQuestionModal&&(Nt(),Cn(),n.addQuestionModal.style.display="flex",n.questionCategorySelect&&n.questionCategorySelect.focus(),S.dragDropInitialized||setTimeout(()=>{Mn(),S.dragDropInitialized=!0},200))}function Be(){n.addQuestionModal&&(n.addQuestionModal.style.display="none",Nt())}function Nt(){Y=!1,editState={isEditing:!1,mode:null,category:null,questionIndex:null,originalQuestion:null};const e=document.querySelector("#add-question-modal .modal-header h3");e&&(e.textContent="Neue Frage hinzufügen");const t=n.confirmQuestionBtn;t&&(t.textContent="Frage hinzufügen",t.disabled=!1),n.questionCategorySelect&&(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',n.questionCategorySelect.disabled=!1),n.questionTextInput&&(n.questionTextInput.value=""),n.questionImageInput&&(n.questionImageInput.value=""),n.questionImageFileInput&&(n.questionImageFileInput.value=""),re(),n.option1Input&&(n.option1Input.value=""),n.option2Input&&(n.option2Input.value=""),n.option3Input&&(n.option3Input.value=""),n.option4Input&&(n.option4Input.value=""),n.correctAnswerSelect&&(n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>'),n.questionExplanationInput&&(n.questionExplanationInput.value=""),n.questionStreetViewUrlInput&&(n.questionStreetViewUrlInput.value=""),n.questionPreview&&(n.questionPreview.style.display="none")}function Cn(){if(!n.questionCategorySelect)return;n.questionCategorySelect.innerHTML="";let e=[];if(l.questions)e=Object.keys(l.questions).filter(t=>t!=="all");else{const t=new Set;["image-based","time-limited"].forEach(o=>{l[o]&&Object.keys(l[o]).forEach(a=>{a!=="all"&&t.add(a)})}),e=Array.from(t)}console.log("=== CATEGORY LOADING DEBUG ==="),console.log("Available categories:",e),console.log("Using unified structure:",!!l.questions),e.length===0?(n.questionCategorySelect.innerHTML='<option value="">Keine Kategorien verfügbar</option>',n.questionCategorySelect.disabled=!0):(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',e.sort().forEach(t=>{const o=document.createElement("option");o.value=t,o.textContent=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),n.questionCategorySelect.appendChild(o)}),n.questionCategorySelect.disabled=!1)}function Tn(e){if(Y){console.log("File upload already in progress, ignoring duplicate event");return}Y=!0;try{const t=e.target.files[0];if(!t){re();return}if(!t.type.startsWith("image/")){g("Bitte wähle eine gültige Bilddatei aus.","warning"),e.target.value="",re();return}if(t.size>10*1024*1024){g("Bilddatei ist zu groß. Maximum: 10MB","warning"),e.target.value="",re();return}n.questionImageInput&&(n.questionImageInput.value="");const o=new FileReader;o.onload=a=>{ft(a.target.result,t.name,t.size)},o.readAsDataURL(t)}finally{setTimeout(()=>{Y=!1},100)}}function An(){if(!n.questionImageInput||!n.imagePreview||!n.previewImg)return;const e=n.questionImageInput.value.trim();e&&X(e)?(n.questionImageFileInput&&(n.questionImageFileInput.value=""),n.previewImg.src=e,n.previewImg.onload=()=>{ft(e,"Externe URL",null)},n.previewImg.onerror=()=>{re(),g("Bild konnte nicht geladen werden. Überprüfe die URL.","warning")}):re()}function ft(e,t,o){!n.imagePreview||!n.previewImg||(n.previewImg.src=e,n.imagePreview.style.display="block",n.imageFilename&&(n.imageFilename.textContent=t),n.imageSize&&o?n.imageSize.textContent=xn(o):n.imageSize&&(n.imageSize.textContent=""))}function re(){n.imagePreview&&(n.imagePreview.style.display="none")}function xn(e){if(e===0)return"0 Bytes";const t=1024,o=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,a)).toFixed(2))+" "+o[a]}function Mn(){const e=document.querySelector(".file-upload-label"),t=n.addQuestionModal;if(console.log("🔧 Setting up drag and drop..."),console.log("File upload label found:",!!e),console.log("Add question modal found:",!!t),!e||!t){console.warn("❌ Drag and drop setup failed - missing elements");return}console.log("✅ Drag and drop setup successful"),["dragenter","dragover","dragleave","drop"].forEach(d=>{document.addEventListener(d,o,!1),t.addEventListener(d,o,!1)}),["dragenter","dragover"].forEach(d=>{e.addEventListener(d,a,!1)}),["dragleave","drop"].forEach(d=>{e.addEventListener(d,i,!1)}),e.addEventListener("drop",r,!1),t.addEventListener("drop",c,!1);function o(d){d.preventDefault(),d.stopPropagation()}function a(d){console.log("🎯 Drag enter/over detected"),e.classList.add("drag-over")}function i(d){console.log("🎯 Drag leave/drop detected"),e.classList.remove("drag-over")}function r(d){if(console.log("📁 Drop event triggered"),Y){console.log("File upload already in progress, ignoring drop event");return}const f=d.dataTransfer.files;if(console.log("Files dropped:",f.length),f.length>0){const E=f[0];if(!E.type.startsWith("image/")){g("Bitte ziehe eine gültige Bilddatei hierher.","warning");return}if(E.size>10*1024*1024){g("Bilddatei ist zu groß. Maximum: 10MB","warning");return}if(Y=!0,n.questionImageFileInput){const z=new DataTransfer;z.items.add(E),n.questionImageFileInput.files=z.files,n.questionImageInput&&(n.questionImageInput.value="");const A=new FileReader;A.onload=N=>{ft(N.target.result,E.name,E.size),setTimeout(()=>{Y=!1},100)},A.readAsDataURL(E)}g("Bild erfolgreich hochgeladen!","success")}}function c(d){d.target.closest(".file-upload-label")||(console.log("📁 Modal drop event triggered"),r(d))}}function Qn(){var o,a,i,r;if(!n.correctAnswerSelect)return;const e=[(o=n.option1Input)==null?void 0:o.value.trim(),(a=n.option2Input)==null?void 0:a.value.trim(),(i=n.option3Input)==null?void 0:i.value.trim(),(r=n.option4Input)==null?void 0:r.value.trim()].filter(c=>c&&c.length>0),t=n.correctAnswerSelect.value;n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>',e.forEach(c=>{const d=document.createElement("option");d.value=c,d.textContent=c,n.correctAnswerSelect.appendChild(d)}),t&&e.includes(t)&&(n.correctAnswerSelect.value=t)}function X(e){try{return new URL(e),!0}catch{return!1}}async function Dn(){var i,r,c,d;const e=(i=n.categoryNameInput)==null?void 0:i.value.trim();(r=n.categoryDescriptionInput)==null||r.value.trim();const t=[];if((c=n.modeImageBased)!=null&&c.checked&&t.push("image-based"),(d=n.modeTimeLimited)!=null&&d.checked&&t.push("time-limited"),!e){g("Bitte gib einen Kategorie-Namen ein.","warning");return}if(!zn(e)){g("Kategorie-Name darf nur Kleinbuchstaben, Zahlen und Unterstriche enthalten.","warning");return}if(t.length===0){g("Bitte wähle mindestens einen Quiz-Modus aus.","warning");return}const o=[];if(t.forEach(u=>{let f=u;u==="time-limited"&&(f="image-based"),l[f]&&l[f][e]&&o.push(u)}),o.length>0){const u=o.join(", ");g(`Kategorie "${e}" existiert bereits in: ${u}`,"warning");return}t.forEach(u=>{let f=u;u==="time-limited"&&(f="image-based"),l[f]||(l[f]={}),l[f][e]=[]});try{await en(e,t)?console.log("Category saved to backend successfully"):console.warn("Failed to save category to backend, using localStorage backup")}catch(u){console.error("Error saving category to backend:",u)}be(),_(),Se();const a=t.join(", ");g(`Kategorie "${e}" erfolgreich zu ${a} hinzugefügt!`,"success")}function zn(e){return/^[a-z0-9_]+$/.test(e)}async function $n(e){var E,z,A,N,O,M,Q,w,Qe,De,ht;if(e&&(e.preventDefault(),e.stopPropagation()),n.confirmQuestionBtn){if(n.confirmQuestionBtn.disabled){console.log("Question submission already in progress, ignoring duplicate request");return}n.confirmQuestionBtn.disabled=!0,n.confirmQuestionBtn.textContent="Wird hinzugefügt..."}const t=(E=n.questionCategorySelect)==null?void 0:E.value,o=(z=n.questionTextInput)==null?void 0:z.value.trim(),a=(A=n.questionImageInput)==null?void 0:A.value.trim(),i=(N=n.questionImageFileInput)==null?void 0:N.files[0],r=(O=n.questionExplanationInput)==null?void 0:O.value.trim(),c=(M=n.questionStreetViewUrlInput)==null?void 0:M.value.trim(),d=[(Q=n.option1Input)==null?void 0:Q.value.trim(),(w=n.option2Input)==null?void 0:w.value.trim(),(Qe=n.option3Input)==null?void 0:Qe.value.trim(),(De=n.option4Input)==null?void 0:De.value.trim()].filter(L=>L&&L.length>0),u=(ht=n.correctAnswerSelect)==null?void 0:ht.value,f=()=>{n.confirmQuestionBtn&&(n.confirmQuestionBtn.disabled=!1,n.confirmQuestionBtn.textContent="Frage hinzufügen")};try{if(console.log("=== QUESTION SUBMISSION DEBUG ==="),console.log("Category:",t),console.log("Question Text:",o),console.log("Image URL:",a),console.log("Image File:",i),console.log("Options:",d),console.log("Correct Answer:",u),console.log("Explanation:",r),console.log("Street View URL:",c),!t){g("Bitte wähle eine Kategorie aus.","warning"),f();return}if(t.toLowerCase()==="all"){g('Die "All"-Kategorie wird automatisch verwaltet. Bitte wähle eine spezifische Kategorie.',"warning"),f();return}if(!i&&(!a||!X(a))){g("Ein Bild (Upload oder URL) ist für alle Fragen erforderlich.","warning"),f();return}if(d.length<2){g("Bitte gib mindestens 2 Antwortmöglichkeiten ein.","warning"),f();return}if(!u){g("Bitte wähle die richtige Antwort aus.","warning"),f();return}if(!d.includes(u)){g("Die richtige Antwort muss eine der Antwortmöglichkeiten sein.","warning"),f();return}const L={options:d,correctAnswer:u};o&&(L.question=o),r&&(L.explanation=r),c&&X(c)&&(L.streetViewUrl=c),a&&X(a)&&(L.image=a);const G=new FormData;G.append("mode","both"),G.append("category",t),G.append("questionData",JSON.stringify(L));const Et=Date.now()+"_"+Math.random().toString(36).substring(2,11);G.append("requestId",Et),i&&(!a||!X(a))&&(G.append("image",i),console.log("Adding image file to form data:",i.name,"size:",i.size)),console.log("Submitting question with request ID:",Et),console.log("Form data contents:");for(let[de,ue]of G.entries())console.log(`  ${de}:`,ue);const Z=await fetch("/api/add-question",{method:"POST",body:G});if(console.log("Response status:",Z.status),console.log("Response ok:",Z.ok),!Z.ok){const de=await Z.text();console.error("Server error response:",de);let ue;try{ue=JSON.parse(de)}catch{ue={error:de||"Unknown server error"}}throw new Error(ue.error||`HTTP ${Z.status}: ${Z.statusText}`)}const we=await Z.json();console.log("Question added successfully:",we),we.imagePath&&(L.image=we.imagePath),l.questions||(l.questions={}),l.questions[t]||(l.questions[t]=[]),l.questions[t].push(L);let oe=mode;mode==="time-limited"&&(oe="image-based"),l[oe]||(l[oe]={}),l[oe][t]||(l[oe][t]=[]),l[oe][t].push(L);const Zt=we.imagePath?" (Bild automatisch organisiert)":"";g(`Frage erfolgreich zu "${t}" (beide Quiz-Modi) hinzugefügt!${Zt}`,"success"),be(),_(),Me(),Be()}catch(L){console.error("Error adding question:",L),g(`Fehler beim Hinzufügen der Frage: ${L.message}`,"error")}finally{f()}}function Nn(){var u,f,E,z,A,N,O,M,Q;if(!n.questionPreview)return;const e=(u=n.questionTextInput)==null?void 0:u.value.trim(),t=(f=n.questionImageInput)==null?void 0:f.value.trim(),o=(E=n.questionImageFileInput)==null?void 0:E.files[0],a=(z=n.questionStreetViewUrlInput)==null?void 0:z.value.trim(),i=[(A=n.option1Input)==null?void 0:A.value.trim(),(N=n.option2Input)==null?void 0:N.value.trim(),(O=n.option3Input)==null?void 0:O.value.trim(),(M=n.option4Input)==null?void 0:M.value.trim()].filter(w=>w&&w.length>0),r=(Q=n.correctAnswerSelect)==null?void 0:Q.value;let c="";if(e&&(c+=`<div style="margin-bottom: 1rem; font-weight: 600;">${e}</div>`),t&&X(t))c+=`<img src="${t}" alt="Frage-Bild" class="preview-image">`;else if(o){const w=n.previewImg;w&&w.src&&(c+=`<img src="${w.src}" alt="Frage-Bild" class="preview-image">`)}i.length>0&&(c+='<div class="preview-options">',i.forEach(w=>{c+=`<div class="${w===r?"preview-option correct":"preview-option"}">${w}</div>`}),c+="</div>"),a&&X(a)&&(c+=`<div style="margin-top: 1rem; padding: 0.5rem; background: #e8f4fd; border-radius: 4px; font-size: 0.9rem;">
            🌍 Street View verfügbar: Spieler können nach der Antwort die Location besuchen
        </div>`);const d=n.questionPreview.querySelector(".preview-content");d&&(d.innerHTML=c),n.questionPreview.style.display="block"}function On(){try{const e=JSON.stringify(l,null,2),t=new Blob([e],{type:"application/json"}),o=document.createElement("a");o.href=URL.createObjectURL(t),o.download=`terraTueftler-quizData-${new Date().toISOString().split("T")[0]}.json`,o.click(),g("Quiz-Daten erfolgreich exportiert!","success")}catch(e){console.error("Export error:",e),g("Fehler beim Exportieren der Daten.","error")}}function Fn(){const e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=t=>{const o=t.target.files[0];if(!o)return;const a=new FileReader;a.onload=i=>{try{const r=JSON.parse(i.target.result);if(!Pn(r)){g("Ungültige Datenstruktur. Bitte überprüfe die JSON-Datei.","error");return}confirm("Möchtest du die aktuellen Quiz-Daten durch die importierten Daten ersetzen? Diese Aktion kann nicht rückgängig gemacht werden.")&&(be(),Object.keys(r).forEach(c=>{l[c]=r[c]}),_(),g("Quiz-Daten erfolgreich importiert!","success"))}catch(r){console.error("Import error:",r),g("Fehler beim Importieren der Daten. Überprüfe das JSON-Format.","error")}},a.readAsText(o)},e.click()}function Pn(e){if(typeof e!="object"||e===null)return!1;const t=["image-based","time-limited"];for(const o in e)if(t.includes(o)){if(typeof e[o]!="object"||e[o]===null)return!1;for(const a in e[o]){if(!Array.isArray(e[o][a]))return!1;for(const i of e[o][a])if(!i.options||!Array.isArray(i.options)||i.options.length<2||!i.correctAnswer||!i.options.includes(i.correctAnswer))return!1}}return!0}function be(){try{const t={timestamp:new Date().toISOString(),data:l};localStorage.setItem("terraTueftlerQuizDataBackup",JSON.stringify(t)),console.log("Quiz data backup saved to localStorage")}catch(e){console.error("Error saving backup:",e)}}function Ot(){n.closeStatsModal&&n.closeStatsModal.addEventListener("click",$e),n.closeStatsBtn&&n.closeStatsBtn.addEventListener("click",$e),n.leaderboardStatsModal&&n.leaderboardStatsModal.addEventListener("click",e=>{e.target===n.leaderboardStatsModal&&$e()})}async function Un(){if(confirm("Möchtest du ALLE Ranglisten-Daten wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."))try{await un(),_(),g("Alle Ranglisten-Daten wurden erfolgreich gelöscht!","success")}catch(e){console.error("Clear error:",e),g("Fehler beim Löschen der Ranglisten-Daten.","error")}}async function jn(){if(n.leaderboardStatsModal)try{const e=qt();if(n.statsTotalEntries&&(n.statsTotalEntries.textContent=e.totalEntries),n.statsTotalPlayers&&(n.statsTotalPlayers.textContent=e.totalPlayers),n.statsLastUpdated){const t=e.lastUpdated?new Date(e.lastUpdated).toLocaleString("de-DE"):"Nie";n.statsLastUpdated.textContent=t}n.topPerformersList&&(n.topPerformersList.innerHTML="",e.topPerformers.length===0?n.topPerformersList.innerHTML="<p>Keine Leistungen vorhanden</p>":e.topPerformers.forEach((t,o)=>{const a=document.createElement("div");a.className="performer-item",a.innerHTML=`
                        <span class="rank">${o+1}.</span>
                        <span class="name">${t.name}</span>
                        <span class="score">${t.correctAnswers}/${t.totalQuestions}</span>
                        <span class="accuracy">${t.accuracy.toFixed(1)}%</span>
                        <span class="mode">${t.mode}</span>
                        <span class="category">${t.category}</span>
                    `,n.topPerformersList.appendChild(a)})),n.categoryStats&&(n.categoryStats.innerHTML="",Object.keys(e.categoryStats).forEach(t=>{const o=document.createElement("div");o.className="mode-stats",o.innerHTML=`<h5>${ve(t)}</h5>`,Object.keys(e.categoryStats[t]).forEach(a=>{const i=e.categoryStats[t][a],r=document.createElement("div");r.className="category-stat",r.innerHTML=`
                        <span class="category-name">${a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," ")}</span>
                        <span class="category-count">${i} Einträge</span>
                    `,o.appendChild(r)}),n.categoryStats.appendChild(o)})),n.leaderboardStatsModal.style.display="flex"}catch(e){console.error("Error showing leaderboard stats:",e),g("Fehler beim Laden der Statistiken.","error")}}function $e(){n.leaderboardStatsModal&&(n.leaderboardStatsModal.style.display="none")}function ve(e){switch(e){case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}async function Rn(){if(console.log("Starting data migration to unified structure..."),!l)return console.error("Quiz data not available for migration"),!1;l.questions||(l.questions={});const e=new Map;let t=0,o=0;["image-based","time-limited"].forEach(i=>{l[i]&&Object.keys(l[i]).forEach(r=>{r!=="all"&&Array.isArray(l[i][r])&&l[i][r].forEach(c=>{const d=`${c.image||"no-image"}_${c.correctAnswer}_${r}`;e.has(d)?o++:(l.questions[r]||(l.questions[r]=[]),l.questions[r].push(c),e.set(d,!0),t++)})})}),console.log(`Migration completed: ${t} questions migrated, ${o} duplicates removed`);try{return await Xt(l),be(),!0}catch(i){return console.error("Error saving migrated data:",i),!1}}function Hn(){if(!l)return!1;if(!l.questions||Object.keys(l.questions).length===0){const e=["image-based","time-limited"];for(const t of e)if(l[t]&&Object.keys(l[t]).length>0)return!0}return!1}function Kn(){n.contentManagementModal&&(n.contentManagementModal.style.display="flex",Pt(),Ut(),Me(),pt())}function Ne(){n.contentManagementModal&&(n.contentManagementModal.style.display="none")}function Ft(){n.closeContentModal&&n.closeContentModal.addEventListener("click",Ne),n.closeContentBtn&&n.closeContentBtn.addEventListener("click",Ne),n.categoriesTab&&n.categoriesTab.addEventListener("click",Pt),n.questionsTab&&n.questionsTab.addEventListener("click",Vn),n.filterMode&&n.filterMode.addEventListener("change",wt),n.filterCategory&&n.filterCategory.addEventListener("change",wt),n.contentManagementModal&&n.contentManagementModal.addEventListener("click",e=>{e.target===n.contentManagementModal&&Ne()})}function Pt(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.categoriesTab.classList.add("active"),n.questionsTab.classList.remove("active"),n.categoriesContent.classList.add("active"),n.questionsContent.classList.remove("active"))}function Vn(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.questionsTab.classList.add("active"),n.categoriesTab.classList.remove("active"),n.questionsContent.classList.add("active"),n.categoriesContent.classList.remove("active"),pt())}function Ut(){if(!n.categoriesList)return;const e=new Set,t={};if(Object.keys(l).forEach(o=>{Object.keys(l[o]).forEach(a=>{e.add(a),t[a]||(t[a]={modes:[],totalQuestions:0}),t[a].modes.push(o),t[a].totalQuestions+=l[o][a].length})}),n.categoriesList.innerHTML="",e.size===0){n.categoriesList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">📁</div>
                <p>Keine Kategorien vorhanden</p>
            </div>
        `;return}Array.from(e).sort().forEach(o=>{const a=t[o],i=document.createElement("div");i.className="content-item";const r=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," "),c=a.modes.map(d=>ve(d)).join(", ");i.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">${r}</div>
                <div class="content-item-meta">
                    <span>Modi: ${c}</span>
                    <span>${a.totalQuestions} Fragen</span>
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteCategory('${o}')">
                    🗑️ Löschen
                </button>
            </div>
        `,n.categoriesList.appendChild(i)})}function Me(){if(!n.questionsList)return;const e=[];Object.keys(l).forEach(t=>{Object.keys(l[t]).forEach(o=>{l[t][o].forEach((a,i)=>{e.push({mode:t,category:o,index:i,question:a,displayName:o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," ")})})})}),jt(e)}function jt(e){if(n.questionsList){if(n.questionsList.innerHTML="",e.length===0){n.questionsList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">❓</div>
                <p>Keine Fragen vorhanden</p>
            </div>
        `;return}e.forEach(t=>{const o=document.createElement("div");o.className="content-item";const a=t.question.question||"Bildbasierte Frage",i=!!t.question.image,r=i?`<img src="${t.question.image}" class="question-preview-mini" alt="Vorschau">`:"";o.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">
                    ${r}
                    <span class="question-text-preview">${a}</span>
                </div>
                <div class="content-item-meta">
                    <span>Kategorie: ${t.displayName}</span>
                    <span>Modus: ${ve(t.mode)}</span>
                    <span>Antwort: ${t.question.correctAnswer}</span>
                    ${i?"<span>📷 Mit Bild</span>":""}
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteQuestion('${t.mode}', '${t.category}', ${t.index})">
                    🗑️ Löschen
                </button>
            </div>
        `,n.questionsList.appendChild(o)})}}function pt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value;n.filterMode.innerHTML=`
        <option value="">Alle Modi</option>
        <option value="image-based">Bildbasiert</option>
        <option value="time-limited">Zeitbegrenzt</option>
    `,n.filterMode.value=e;const t=new Set;Object.keys(l).forEach(a=>{Object.keys(l[a]).forEach(i=>{t.add(i)})});const o=n.filterCategory.value;n.filterCategory.innerHTML='<option value="">Alle Kategorien</option>',Array.from(t).sort().forEach(a=>{const i=a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," "),r=document.createElement("option");r.value=a,r.textContent=i,n.filterCategory.appendChild(r)}),n.filterCategory.value=o}function wt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value,t=n.filterCategory.value,o=[];Object.keys(l).forEach(a=>{a!=="questions"&&(e&&a!==e||l[a]&&typeof l[a]=="object"&&Object.keys(l[a]).forEach(i=>{t&&i!==t||Array.isArray(l[a][i])&&l[a][i].forEach((r,c)=>{o.push({mode:a,category:i,index:c,question:r,displayName:i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ")})})}))}),jt(o)}let D={type:null,data:null};function Rt(){n.closeDeleteModal&&n.closeDeleteModal.addEventListener("click",Le),n.cancelDeleteBtn&&n.cancelDeleteBtn.addEventListener("click",Le),n.confirmDeleteBtn&&n.confirmDeleteBtn.addEventListener("click",Jn),n.deleteConfirmationModal&&n.deleteConfirmationModal.addEventListener("click",e=>{e.target===n.deleteConfirmationModal&&Le()})}function Le(){n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="none",D={type:null,data:null})}window.confirmDeleteCategory=function(e){const t=_n(e);if(D={type:"category",data:{categoryName:e}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Kategorie löschen"),n.deleteModalMessage&&(n.deleteModalMessage.textContent=`Möchtest du die Kategorie "${e}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`),n.deleteModalDetails){const o=e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ");n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Kategorie:</strong> ${o}</li>
                <li><strong>Betroffene Modi:</strong> ${t.modes.map(a=>ve(a)).join(", ")}</li>
                <li><strong>Anzahl Fragen:</strong> ${t.totalQuestions}</li>
                <li><strong>Bilder:</strong> ${t.imageCount} Dateien werden gelöscht</li>
                <li><strong>Ordner:</strong> Kategorie-Ordner wird entfernt (falls leer)</li>
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Kategorie löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};window.confirmDeleteQuestion=function(e,t,o){const a=l[e]&&l[e][t]&&l[e][t][o];if(!a){g("Frage nicht gefunden.","error");return}D={type:"question",data:{mode:e,category:t,questionIndex:o}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Frage löschen");const i=a.question||"Bildbasierte Frage";if(n.deleteModalMessage&&(n.deleteModalMessage.textContent="Möchtest du diese Frage wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."),n.deleteModalDetails){const r=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),c=!!a.image;n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Frage:</strong> ${i}</li>
                <li><strong>Kategorie:</strong> ${r}</li>
                <li><strong>Modus:</strong> ${ve(e)}</li>
                <li><strong>Richtige Antwort:</strong> ${a.correctAnswer}</li>
                ${c?`<li><strong>Bild:</strong> ${a.image} wird gelöscht</li>`:""}
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Frage löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};function _n(e){const t={modes:[],totalQuestions:0,imageCount:0};return Object.keys(l).forEach(o=>{if(l[o][e]){t.modes.push(o);const a=l[o][e];t.totalQuestions+=a.length,a.forEach(i=>{i.image&&i.image.startsWith("assets/images/")&&t.imageCount++})}}),t}async function Jn(){if(!(!D.type||!D.data)){n.deleteBtnText&&(n.deleteBtnText.style.display="none"),n.deleteLoading&&(n.deleteLoading.style.display="inline-block"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!0);try{D.type==="category"?await Gn(D.data.categoryName):D.type==="question"&&await Zn(D.data.mode,D.data.category,D.data.questionIndex)}catch(e){console.error("Deletion error:",e),g(`Fehler beim Löschen: ${e.message}`,"error")}finally{n.deleteBtnText&&(n.deleteBtnText.style.display="inline"),n.deleteLoading&&(n.deleteLoading.style.display="none"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!1),Le()}}}async function Gn(e){try{const t=await an(e);Object.keys(l).forEach(a=>{l[a][e]&&delete l[a][e]}),l.questions&&l.questions[e]&&(delete l.questions[e],console.log(`Removed category "${e}" from unified structure`)),be(),await Tt(),_(),Ut(),Me(),pt();const o=t.deletedQuestions>0?` (${t.deletedQuestions} Fragen, ${t.deletedImages.length} Bilder gelöscht)`:"";g(`Kategorie "${e}" erfolgreich gelöscht!${o}`,"success")}catch(t){throw console.error("Error deleting category:",t),new Error(`Kategorie konnte nicht gelöscht werden: ${t.message}`)}}async function Zn(e,t,o){try{console.log("=== DELETE QUESTION DEBUG ==="),console.log("Mode:",e),console.log("Category:",t),console.log("Question Index:",o);let a=e;e==="time-limited"&&(a="image-based");const i=l[a]&&l[a][t]&&l[a][t][o];if(console.log("Question to delete:",i),!i)throw new Error("Frage nicht gefunden");const r=await on(e,t,o);console.log("Backend deletion result:",r),await Tt(),console.log("Quiz data refreshed from server"),_(),Me();const c=r.deletedImagePath?" (Bild gelöscht)":"";g(`Frage erfolgreich gelöscht!${c}`,"success")}catch(a){throw console.error("Error deleting question:",a),new Error(`Frage konnte nicht gelöscht werden: ${a.message}`)}}const y={currentSection:"home",playerName:"Anonym"};let se,F,yt,Ht,H,_e,Je,ye,W,xe,ge,ne,q,$,U,he,qe,Ge,Ze,K,V,We,Xe,Ye,et,tt,nt,ot,at,it,rt,st;function Wn(){se=document.getElementById("nav-links"),F=document.getElementById("burger"),yt=document.querySelectorAll("main > section"),Ht=document.querySelectorAll(".nav-links a[data-target]"),document.querySelectorAll(".btn[data-target]"),document.getElementById("player-name-area"),H=document.getElementById("player-name"),_e=document.getElementById("save-player-name"),Je=document.getElementById("current-player-name"),document.getElementById("learn"),document.getElementById("learn-overview"),ye=document.getElementById("learn-category-select"),W=document.getElementById("learn-content-grid"),xe=document.getElementById("sound-toggle"),ge=document.getElementById("theme-select"),ne=document.getElementById("time-limit-select"),q=document.getElementById("time-limit-options"),$=document.getElementById("leaderboard-mode-select"),U=document.getElementById("leaderboard-category-select"),he=document.getElementById("leaderboard-time-select"),qe=document.getElementById("time-limit-filter"),Ge=document.getElementById("current-player-display"),Ze=document.getElementById("change-player-btn"),K=document.getElementById("player-name-modal"),V=document.getElementById("new-player-name"),We=document.getElementById("close-player-modal"),Xe=document.getElementById("cancel-player-change"),Ye=document.getElementById("confirm-player-change"),et=document.querySelectorAll("#quiz .quiz-option[data-quiz-type]"),st=document.getElementById("quiz-category-options"),tt=document.getElementById("back-to-quiz-selection"),nt=document.getElementById("submit-answer"),ot=document.getElementById("next-question"),at=document.getElementById("prev-question"),it=document.getElementById("quit-quiz"),rt=document.getElementById("finish-quiz")}function Kt(e){for(let t=e.length-1;t>0;t--){const o=Math.floor(Math.random()*(t+1));[e[t],e[o]]=[e[o],e[t]]}return e}function g(e,t="info"){const o=document.createElement("div");switch(o.textContent=e,o.style.position="fixed",o.style.bottom="20px",o.style.left="50%",o.style.transform="translateX(-50%)",o.style.padding="10px 20px",o.style.borderRadius="var(--radius)",o.style.color="#fff",o.style.zIndex="2000",o.style.boxShadow="var(--shadow-lg)",o.style.textAlign="center",o.style.opacity="0",o.style.transition="opacity 0.5s ease",t){case"warning":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"error":o.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"info":default:o.style.backgroundColor="var(--clr-secondary)";break}document.body.appendChild(o),requestAnimationFrame(()=>{o.style.opacity="1"}),setTimeout(()=>{o.style.opacity="0",setTimeout(()=>{o.parentNode&&o.remove()},500)},3e3)}function j(e){if(document.getElementById(e)||(console.error(`Section with ID "${e}" not found. Defaulting to home.`),e="home"),y.currentSection=e,yt.forEach(o=>{const a=o.id===e;o.style.display=a?"block":"none"}),Ht.forEach(o=>{o.classList.toggle("active",o.dataset.target===e)}),se&&F&&se.classList.contains("show")&&(se.classList.remove("show"),F.classList.remove("change"),F.setAttribute("aria-expanded","false")),e!=="quiz-game"&&e!=="quiz-category"&&Ee(),e!=="quiz"&&q){q.style.display="none";const o=document.getElementById("start-time-limited-quiz"),a=document.getElementById("time-limit-instruction");o&&(o.style.display="none"),a&&(a.style.display="none")}e==="learn"&&(Gt("all"),ye&&(ye.value="all")),e==="leaderboard"&&Xn(),e==="admin"&&Sn(),e==="quiz"&&lt(),window.scrollTo(0,0)}function Xn(){!$||!U||(Vt(),_t(),fe())}function Vt(){if(!$||!U)return;const e=$.value;U.innerHTML="";let t=e;if(e==="time-limited"&&(t="image-based"),!l[t])return;const o=Object.keys(l[t]);let a;a=o,a.forEach(i=>{const r=document.createElement("option");r.value=i,r.textContent=i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," "),U.appendChild(r)})}function _t(){if(!qe||!$)return;$.value==="time-limited"?qe.style.display="block":qe.style.display="none"}function fe(){if(!$||!U)return;const e=$.value,t=U.value,o=e==="time-limited"&&he?parseFloat(he.value):null;kn(e,t,o)}window.updateLeaderboardDisplay=fe;function lt(){Ge&&(Ge.textContent=`Aktueller Spieler: ${y.playerName}`)}function Yn(){K&&V&&(V.value=y.playerName==="Anonym"?"":y.playerName,K.style.display="flex",V.focus())}function pe(){K&&(K.style.display="none")}function It(){if(!V)return;const e=V.value.trim();e&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",e),ce(),lt(),pe(),g(`Spieler geändert zu: ${e}`,"info")):e.length>20?g("Name darf maximal 20 Zeichen haben.","warning"):(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName","Anonym"),ce(),lt(),pe(),g('Spieler auf "Anonym" zurückgesetzt.',"info"))}function ce(){H&&(H.value=y.playerName),Je&&(Je.textContent=`Aktueller Spieler: ${y.playerName}`)}function kt(){if(!H)return;const e=H.value.trim();e&&e.length>0&&e.length<=20?(y.playerName=e,localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce(),g("Name gespeichert!","info")):(g("Bitte gib einen gültigen Namen ein (1-20 Zeichen).","warning"),H.value=y.playerName)}function Jt(){if(!y.playerName||y.playerName==="Anonym"){const e=prompt("Bitte gib deinen Spielernamen für die Rangliste ein (max. 20 Zeichen):",y.playerName!=="Anonym"?y.playerName:"");return e&&e.trim()!==""&&e.trim().length<=20?(y.playerName=e.trim(),localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce(),!0):e!==null?(alert("Ungültiger Name. Bitte 1-20 Zeichen verwenden."),Jt()):(localStorage.getItem("terraTueftlerPlayerName")||(y.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName",y.playerName),ce()),!1)}return!0}function eo(){if(s.currentQuizType="time-limited",q){q.style.display="block";let e=document.getElementById("time-limit-instruction");if(e)e.style.display="block";else{e=document.createElement("p"),e.id="time-limit-instruction",e.textContent="Wähle dein gewünschtes Zeitlimit pro Frage:",e.style.marginBottom="0.5rem",e.style.fontWeight="600";const o=q.querySelector("select");o?q.insertBefore(e,o):q.appendChild(e)}let t=document.getElementById("start-time-limited-quiz");t?t.style.display="block":(t=document.createElement("button"),t.id="start-time-limited-quiz",t.className="btn",t.textContent="Quiz starten",t.style.marginTop="1rem",t.style.display="block",t.style.width="100%",t.addEventListener("click",()=>{ne&&(s.selectedTimeLimit=parseFloat(ne.value)||1),q.style.display="none",Te("time-limited")}),q.appendChild(t))}else console.error("Time limit options div not found"),Te("time-limited")}function Gt(e="all"){if(!W){console.warn("Learning content grid not found");return}if(!l){console.error("Quiz data not available for learning content"),W.innerHTML='<p class="no-content">Lerndaten konnten nicht geladen werden.</p>';return}let t=[];try{if(l.questions)e==="all"?Object.keys(l.questions).forEach(a=>{if(a!=="all"&&l.questions[a]){const i=l.questions[a];Array.isArray(i)&&i.forEach(r=>{r.image&&t.push({...r,category:a,sourceType:"unified"})})}}):l.questions[e]&&Array.isArray(l.questions[e])&&l.questions[e].forEach(a=>{a.image&&t.push({...a,category:e,sourceType:"unified"})});else{const a=["image-based"],i=new Set;e==="all"?a.forEach(r=>{l[r]&&Object.keys(l[r]).forEach(c=>{if(c!=="all"&&l[r][c]){const d=l[r][c];Array.isArray(d)&&d.forEach(u=>{u.image&&!i.has(u.image)&&(i.add(u.image),t.push({...u,category:c,sourceType:r}))})}})}):a.forEach(r=>{l[r]&&l[r][e]&&Array.isArray(l[r][e])&&l[r][e].forEach(c=>{c.image&&!i.has(c.image)&&(i.add(c.image),t.push({...c,category:e,sourceType:r}))})})}}catch(a){console.error("Error processing learning content:",a),W.innerHTML='<p class="no-content">Fehler beim Laden der Lerninhalte.</p>';return}if(W.innerHTML="",t.length===0){W.innerHTML='<p class="no-content">Keine Lerninhalte für diese Kategorie verfügbar.</p>';return}console.log(`Learning content loaded: ${t.length} items from category "${e}"`);const o=t.reduce((a,i)=>(a[i.sourceType]=(a[i.sourceType]||0)+1,a),{});console.log("Content by source:",o),t.sort((a,i)=>a.category!==i.category?a.category.localeCompare(i.category):a.correctAnswer.localeCompare(i.correctAnswer)),t.forEach(a=>{const i=to(a);W.appendChild(i)})}function to(e){const t=document.createElement("div");t.className="learn-reference-item";const o=e.image||"https://placehold.co/400x200/bdc3c7/2c3e50?text=Kein+Bild",a=e.category.charAt(0).toUpperCase()+e.category.slice(1).replace(/_/g," "),i=e.sourceType?`<span class="learn-reference-source" title="Quelle: ${e.sourceType}">${no(e.sourceType)}</span>`:"";return t.innerHTML=`
        <img src="${o}" alt="Lerninhalt" class="learn-reference-image" loading="lazy">
        <div class="learn-reference-content">
            <div class="learn-reference-answer">${e.correctAnswer}</div>
            <div class="learn-reference-explanation">${e.explanation||"Keine zusätzlichen Informationen verfügbar."}</div>
            <div class="learn-reference-meta">
                <div class="learn-reference-category">${a}</div>
                ${i}
            </div>
        </div>
    `,t}function no(e){switch(e){case"image-based":return"Bildrätsel";case"time-limited":return"Zeitlimit";default:return e}}let St=!1;function oo(){if(St){console.log("Event listeners already set up, skipping duplicate setup");return}console.log("Setting up event listeners"),St=!0,F&&se&&F.addEventListener("click",()=>{const e=F.getAttribute("aria-expanded")==="true";F.setAttribute("aria-expanded",String(!e)),se.classList.toggle("show"),F.classList.toggle("change")}),document.querySelectorAll(".nav-links a[data-target], .btn[data-target]").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const o=e.dataset.target;o&&j(o)})}),_e&&H&&(_e.addEventListener("click",kt),H.addEventListener("keypress",e=>{e.key==="Enter"&&kt()})),et&&et.forEach(e=>{e.addEventListener("click",()=>{const t=e.dataset.quizType;if(t){if(!l||Object.keys(l).length===0){g("Quiz-Daten werden noch geladen. Bitte warten Sie einen Moment.","warning");return}if(Jt())if(t==="time-limited")eo();else{if(q){q.style.display="none";const o=document.getElementById("start-time-limited-quiz"),a=document.getElementById("time-limit-instruction");o&&(o.style.display="none"),a&&(a.style.display="none")}Te(t)}}})}),ne&&ne.addEventListener("change",e=>{s.selectedTimeLimit=parseFloat(e.target.value)||1}),st&&st.addEventListener("click",e=>{const t=e.target.closest(".quiz-option[data-category]");if(t){const o=t.dataset.category;s.currentQuizType&&o&&Ve(s.currentQuizType,o)}}),tt&&tt.addEventListener("click",()=>{if(Ee(),q){q.style.display="none";const e=document.getElementById("start-time-limited-quiz"),t=document.getElementById("time-limit-instruction");e&&(e.style.display="none"),t&&(t.style.display="none")}j("quiz")}),nt&&(console.log("Adding event listener to submit button"),nt.addEventListener("click",()=>{console.log("Submit button clicked, calling checkAnswer"),En(!1)})),ot&&ot.addEventListener("click",bn),at&&at.addEventListener("click",vn),rt&&rt.addEventListener("click",Ae),it&&it.addEventListener("click",Ae),ye&&ye.addEventListener("change",e=>{const t=e.target.value;Gt(t)}),$&&$.addEventListener("change",()=>{Vt(),_t(),fe()}),U&&U.addEventListener("change",()=>{fe()}),he&&he.addEventListener("change",()=>{fe()}),Ze&&Ze.addEventListener("click",Yn),We&&We.addEventListener("click",pe),Xe&&Xe.addEventListener("click",pe),Ye&&Ye.addEventListener("click",It),V&&V.addEventListener("keypress",e=>{e.key==="Enter"&&It()}),K&&K.addEventListener("click",e=>{e.target===K&&pe()}),xe&&xe.addEventListener("change",e=>{pn(e.target.checked)}),ge&&ge.addEventListener("change",e=>{At(e.target.value,ge)})}document.addEventListener("DOMContentLoaded",async()=>{console.log("DOM fully loaded and parsed."),await gn(),console.log("Data initialized successfully"),Wn(),ut(),Qt(),y.playerName=localStorage.getItem("terraTueftlerPlayerName")||"Anonym",ce(),fn(ge,xe,oo);const e=window.location.hash.substring(1)||"home",t=Array.from(yt).map(o=>o.id);j(t.includes(e)?e:"home"),ne&&(ne.value=String(s.selectedTimeLimit)),console.log("TerraTüftler App Initialized.")});
