(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))o(a);new MutationObserver(a=>{for(const r of a)if(r.type==="childList")for(const c of r.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&o(c)}).observe(document,{childList:!0,subtree:!0});function i(a){const r={};return a.integrity&&(r.integrity=a.integrity),a.referrerPolicy&&(r.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?r.credentials="include":a.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function o(a){if(a.ep)return;a.ep=!0;const r=i(a);fetch(a.href,r)}})();const tn=window.location.origin+"/api";async function J(e,t={}){const i=`${tn}${e}`,a={...{headers:{"Content-Type":"application/json"}},...t};try{const r=await fetch(i,a);if(!r.ok){const c=await r.json().catch(()=>({error:"Unknown error"}));throw new Error(c.error||`HTTP ${r.status}: ${r.statusText}`)}return await r.json()}catch(r){throw console.error(`API request failed for ${e}:`,r),r}}async function nn(e){try{const t=await J("/quiz-data",{method:"POST",body:JSON.stringify(e)});return console.log("Quiz data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save quiz data to backend:",t),!1}}async function on(e){try{const t=await J("/leaderboard-data",{method:"POST",body:JSON.stringify(e)});return console.log("Leaderboard data saved to backend:",t.message),!0}catch(t){return console.error("Failed to save leaderboard data to backend:",t),!1}}async function an(e,t){try{const i=await J("/add-category",{method:"POST",body:JSON.stringify({categoryName:e,modes:t})});return console.log("Category added to backend:",i.message),!0}catch(i){return console.error("Failed to add category to backend:",i),!1}}async function rn(){try{const e=await J("/quiz-data");return console.log("Quiz data loaded from backend"),e}catch(e){return console.error("Failed to load quiz data from backend:",e),{}}}async function sn(){try{const e=await J("/leaderboard-data");return console.log("Leaderboard data loaded from backend"),e}catch(e){return console.error("Failed to load leaderboard data from backend:",e),{"multiple-choice":{},"image-based":{},"time-limited":{},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}}async function ln(e,t,i){try{const o=await J("/delete-question",{method:"DELETE",body:JSON.stringify({mode:e,category:t,questionIndex:i})});return console.log("Question deleted from backend:",o.message),o}catch(o){throw console.error("Failed to delete question from backend:",o),o}}async function cn(e){try{const t=await J("/delete-category",{method:"DELETE",body:JSON.stringify({categoryName:e})});return console.log("Category deleted from backend:",t.message),t}catch(t){throw console.error("Failed to delete category from backend:",t),t}}async function dt(){try{return await J("/quiz-data"),!0}catch{return console.warn("Backend API not available, falling back to static file loading"),!1}}let u={};async function kt(){try{return await dt()?(u=await sn(),console.log("Leaderboard data loaded from backend API")):(u=await(await fetch("./data/leaderboardData.json")).json(),console.log("Leaderboard data loaded from static file")),await dn(),u}catch(e){return console.error("Failed to load leaderboard data:",e),u=Tt(),u}}function Tt(){return{"multiple-choice":{all:[],architecture:[],straßenschilder:[]},"image-based":{all:[],landschaft:[],städte_erkennen:[],wahrzeichen:[],geographie_extrem:[],architecture:[],straßenschilder:[]},"time-limited":{all:{"0.1":[],"0.5":[],1:[],2:[],3:[]},landschaft:{"0.1":[],"0.5":[],1:[],2:[],3:[]},städte_erkennen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},wahrzeichen:{"0.1":[],"0.5":[],1:[],2:[],3:[]},geographie_extrem:{"0.1":[],"0.5":[],1:[],2:[],3:[]}},_metadata:{version:"1.0",lastUpdated:new Date().toISOString(),totalEntries:0,description:"TerraTüftler Leaderboard Data - Individual session tracking"}}}function Fe(e,t,i=null){let o=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&i&&(o+=`_${i}s`),o}async function dn(){console.log("Checking for localStorage leaderboard data to migrate...");let e=0;const t=["multiple-choice","image-based","time-limited"];for(const i of t)if(u[i])for(const o in u[i])if(i==="time-limited")for(const a in u[i][o]){const r=Fe(i,o,parseFloat(a)),c=St(r);if(c&&c.length>0){const d=u[i][o][a]||[],p=Oe(d,c);u[i][o][a]=p,e+=c.length}}else{const a=Fe(i,o),r=St(a);if(r&&r.length>0){const c=u[i][o]||[],d=Oe(c,r);u[i][o]=d,e+=r.length}}e>0&&(console.log(`Migrated ${e} leaderboard entries from localStorage`),await xe())}function St(e){try{const t=localStorage.getItem(e);return t?JSON.parse(t):[]}catch(t){return console.error("Error reading localStorage leaderboard:",t),[]}}function Oe(e,t){const i=[...e];return t.forEach(o=>{if(!i.some(r=>r.name===o.name&&r.correctAnswers===o.correctAnswers&&r.totalQuestions===o.totalQuestions&&Math.abs(new Date(r.completedAt||r.lastPlayed)-new Date(o.completedAt||o.lastPlayed))<1e3)){const r={name:o.name,correctAnswers:o.correctAnswers,totalQuestions:o.totalQuestions,maxStreak:o.maxStreak||0,completedAt:o.completedAt||o.lastPlayed||new Date().toISOString(),mode:o.mode,category:o.category,timeLimit:o.timeLimit};i.push(r)}}),i.sort((o,a)=>{if(a.correctAnswers!==o.correctAnswers)return a.correctAnswers-o.correctAnswers;const r=o.totalQuestions>0?o.correctAnswers/o.totalQuestions:0,c=a.totalQuestions>0?a.correctAnswers/a.totalQuestions:0;return c!==r?c-r:new Date(a.completedAt||a.lastPlayed)-new Date(o.completedAt||o.lastPlayed)}),i.slice(0,50)}function Mt(e,t="all",i=null){return u[e]?e==="time-limited"?!u[e][t]||!u[e][t][i]?[]:u[e][t][i]||[]:u[e][t]||[]:[]}async function un(e,t,i,o,a,r,c=null){if(!e||!i||!o||t<0)return!1;const p={name:e||"Anonym",correctAnswers:t,totalQuestions:a,maxStreak:r,completedAt:new Date().toISOString(),mode:i,category:o,timeLimit:c},g=Mt(i,o,c);g.push(p);const y=Oe([],g);return i==="time-limited"?(u[i][o]||(u[i][o]={}),u[i][o][c]=y):(u[i]||(u[i]={}),u[i][o]=y),At(),await xe(),mn(i,o,c,y),!0}function At(){let e=0;Object.keys(u).forEach(t=>{t!=="_metadata"&&(t==="time-limited"?Object.keys(u[t]).forEach(i=>{Object.keys(u[t][i]).forEach(o=>{e+=u[t][i][o].length})}):Object.keys(u[t]).forEach(i=>{e+=u[t][i].length}))}),u._metadata={...u._metadata,lastUpdated:new Date().toISOString(),totalEntries:e}}function mn(e,t,i,o){try{const a=Fe(e,t,i);localStorage.setItem(a,JSON.stringify(o))}catch(a){console.error("Error saving to localStorage:",a)}}async function xe(){try{if(await dt()&&await on(u))return console.log("Leaderboard data saved to backend"),!0;const t=JSON.stringify(u,null,2);return localStorage.setItem("terraTueftlerLeaderboardPersistent",t),console.log("Leaderboard data saved to localStorage (fallback)"),!0}catch(e){return console.error("Error saving leaderboard data:",e),!1}}async function gn(){try{const e=localStorage.getItem("terraTueftlerLeaderboardPersistent");e?(u=JSON.parse(e),console.log("Loaded leaderboard data from persistent storage")):await kt()}catch(e){console.error("Error loading persistent leaderboard data:",e),await kt()}return u}async function fn(){const e=JSON.parse(JSON.stringify(u));return localStorage.setItem("terraTueftlerLeaderboardBackup",JSON.stringify({timestamp:new Date().toISOString(),data:e})),u=Tt(),Object.keys(localStorage).forEach(t=>{t.startsWith("terraTueftlerLeaderboard_")&&localStorage.removeItem(t)}),await xe(),!0}function xt(){var a;let e=0,t=new Set,i=[],o={};return Object.keys(u).forEach(r=>{r!=="_metadata"&&(o[r]={},r==="time-limited"?Object.keys(u[r]).forEach(c=>{o[r][c]=0,Object.keys(u[r][c]).forEach(d=>{const p=u[r][c][d];e+=p.length,o[r][c]+=p.length,p.forEach(g=>{t.add(g.name),i.push({...g,accuracy:g.totalQuestions>0?g.correctAnswers/g.totalQuestions*100:0})})})}):Object.keys(u[r]).forEach(c=>{const d=u[r][c];e+=d.length,o[r][c]=d.length,d.forEach(p=>{t.add(p.name),i.push({...p,accuracy:p.totalQuestions>0?p.correctAnswers/p.totalQuestions*100:0})})}))}),i.sort((r,c)=>c.correctAnswers!==r.correctAnswers?c.correctAnswers-r.correctAnswers:c.accuracy-r.accuracy),{totalEntries:e,totalPlayers:t.size,topPerformers:i.slice(0,10),categoryStats:o,lastUpdated:(a=u._metadata)==null?void 0:a.lastUpdated}}async function pn(e,t="all",i=null){if(u[e]){if(e==="time-limited"&&i!==null){const o=String(i);u[e][t]&&u[e][t][o]&&(u[e][t][o]=[])}else u[e][t]&&(u[e][t]=[]);At(),await xe()}}let l={},Qt={};async function yn(){try{if(await dt()){l=await rn(),console.log("Quiz data loaded from backend API");return}l=await(await fetch("./data/quizData.json")).json(),console.log("Quiz data loaded from static file")}catch(e){console.error("Failed to load quiz data:",e)}}async function hn(){try{Qt=await(await fetch("./data/learningData.json")).json(),console.log("Learning data loaded successfully")}catch(e){console.error("Failed to load learning data:",e)}}async function vn(){return await Promise.all([yn(),hn(),gn()]),{quizData:l,learningData:Qt}}const R={soundEnabled:!0,currentTheme:"theme-standard"};function Dt(e,t){["theme-standard","theme-dark"].forEach(a=>document.body.classList.remove(a));const o=e||"theme-standard";document.body.classList.add(o),R.currentTheme=o,localStorage.setItem("terraTueftlerTheme",o),t&&(t.value=o)}function En(e,t,i){const o=localStorage.getItem("terraTueftlerTheme")||"theme-standard";Dt(o,e);const a=localStorage.getItem("terraTueftlerSoundEnabled");R.soundEnabled=a!==null?JSON.parse(a):!0,t&&(t.checked=R.soundEnabled),a===null&&localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled)),typeof i=="function"&&i()}function bn(e){R.soundEnabled=e,localStorage.setItem("terraTueftlerSoundEnabled",JSON.stringify(R.soundEnabled))}function Pe(e){if(!(!R.soundEnabled||!window.AudioContext&&!window.webkitAudioContext))try{const t=new(window.AudioContext||window.webkitAudioContext);let i,o;switch(o=t.createGain(),o.connect(t.destination),o.gain.setValueAtTime(.5,t.currentTime),i=t.createOscillator(),i.connect(o),e){case"correct":i.type="sine",i.frequency.setValueAtTime(523.25,t.currentTime),o.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),i.start(t.currentTime),i.stop(t.currentTime+.3);break;case"incorrect":i.type="square",i.frequency.setValueAtTime(110,t.currentTime),o.gain.exponentialRampToValueAtTime(.001,t.currentTime+.4),i.start(t.currentTime),i.stop(t.currentTime+.4);break;case"quizEnd":const a=[261.63,329.63,391.99,523.25];let r=t.currentTime;a.forEach((c,d)=>{const p=t.createOscillator(),g=t.createGain();p.connect(g),g.connect(t.destination),p.type="triangle",p.frequency.setValueAtTime(c,r+d*.15),g.gain.setValueAtTime(.4,r+d*.15),g.gain.exponentialRampToValueAtTime(.001,r+d*.15+.1),p.start(r+d*.15),p.stop(r+d*.15+.1)});break}setTimeout(()=>{t.state!=="closed"&&t.close().catch(a=>console.warn("Error closing AudioContext:",a))},1e3)}catch(t){console.warn("AudioContext could not be started or used.",t),R.soundEnabled=!1;const i=document.getElementById("sound-toggle");i&&(i.checked=!1)}}console.log("🔧 QUIZ.JS LOADED - Version with question text fix - 2024-fix");const s={currentQuizType:"",currentCategory:"",currentQuestions:[],currentQuestionIndex:0,selectedAnswer:null,userAnswers:[],questionStates:[],score:0,currentStreak:0,maxStreak:0,quizEnded:!1,selectedTimeLimit:1,timerId:null,isTimerRunning:!1,imagePhaseActive:!1};let ze,Ue,ae,E,B,f,I,v,T,Ce,pe,ce,b,C,k,U,x,je,Re,He,te,ne,re;function ut(){ze=document.getElementById("quiz"),ze&&ze.querySelectorAll(".quiz-option[data-quiz-type]"),document.getElementById("time-limit-options"),document.getElementById("time-limit-select"),document.getElementById("quiz-category"),Ue=document.querySelector("#quiz-category h2"),ae=document.getElementById("quiz-category-options"),document.getElementById("back-to-quiz-selection"),E=document.getElementById("quiz-game"),B=document.getElementById("question-text"),f=document.getElementById("question-image"),I=document.getElementById("answer-options"),v=document.getElementById("feedback"),T=document.getElementById("timer"),Ce=document.getElementById("timer-phase"),pe=document.getElementById("time-left"),ce=document.getElementById("prev-question"),b=document.getElementById("submit-answer"),C=document.getElementById("next-question"),k=document.getElementById("quit-quiz"),U=document.getElementById("finish-quiz"),x=document.getElementById("scoreboard"),je=document.getElementById("score"),Re=document.getElementById("final-streak"),He=document.getElementById("score-message"),te=document.getElementById("streak-display"),ne=document.getElementById("current-streak"),document.getElementById("leaderboard"),document.getElementById("leaderboard-mode-select"),re=document.getElementById("leaderboard-list"),document.getElementById("clear-leaderboard")}function Ee(){clearInterval(s.timerId),s.currentQuizType="",s.currentCategory="",s.currentQuestions=[],s.currentQuestionIndex=0,s.selectedAnswer=null,s.userAnswers=[],s.questionStates=[],s.score=0,s.currentStreak=0,s.maxStreak=0,s.quizEnded=!1,s.timerId=null,s.isTimerRunning=!1,s.imagePhaseActive=!1,T&&(T.style.display="none"),v&&(v.style.display="none"),x&&(x.style.display="none"),te&&(te.style.display="none"),b&&(b.style.display="inline-block"),C&&(C.style.display="none"),ce&&(ce.style.display="none"),U&&(U.style.display="none"),k&&(k.style.display="none"),B&&(B.textContent="Frage wird geladen..."),f&&(f.style.display="none"),I&&(I.innerHTML="");const e=E?E.querySelector(".question-container"):null,t=E?E.querySelector(".quiz-controls"):null;e&&(e.style.display="block"),t&&(t.style.display="flex")}function Te(e){if(!ae){console.error("Quiz category options container not found.");return}s.currentQuizType=e,ae.innerHTML="";let t=ft(e);e==="time-limited"&&(t+=` (${s.selectedTimeLimit}s)`),Ue&&(Ue.textContent=`Wähle eine Kategorie (${t})`);let i=[];if(l.questions)i=Object.keys(l.questions);else{let o=e;if(e==="time-limited"&&(o="image-based"),!l[o]){console.error(`Quiz data source "${o}" not found in quizData.`),ae.innerHTML="<p>Fehler: Quiztyp nicht gefunden.</p>",j("quiz-category");return}i=Object.keys(l[o])}i.length===0?ae.innerHTML="<p>Keine Kategorien für diesen Modus verfügbar.</p>":i.forEach(o=>{const a=document.createElement("div");a.classList.add("quiz-option");const r=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," ");a.textContent=r,a.dataset.category=o,ae.appendChild(a)}),j("quiz-category")}function Ke(e,t){var r;Ee(),s.currentQuizType=e,s.currentCategory=t;let i=[];if(l.questions&&l.questions[t])i=l.questions[t];else{let c=e;if(e==="time-limited"&&(c="image-based"),!((r=l[c])!=null&&r[t])||l[c][t].length===0){alert("Fehler: Quiz konnte nicht geladen werden. Kategorie oder Typ ungültig."),j("quiz-category");return}i=l[c][t]}if(i.length===0){alert("Fehler: Keine Fragen in dieser Kategorie verfügbar."),j("quiz-category");return}s.currentQuestions=Gt([...i]),s.userAnswers=new Array(s.currentQuestions.length).fill(null),s.questionStates=s.currentQuestions.map(()=>({answered:!1,locked:!1})),E||ut();const o=E==null?void 0:E.querySelector(".question-container"),a=E==null?void 0:E.querySelector(".quiz-controls");o&&(o.style.display="block"),a&&(a.style.display="flex"),B&&(B.style.display="block"),I&&(I.style.display="grid"),x&&(x.style.display="none"),te&&(te.style.display="block"),ne&&(ne.textContent=s.currentStreak),k&&(k.style.display="inline-block"),j("quiz-game"),mt(),e==="time-limited"&&T&&pe&&Ce?gt():T&&(T.style.display="none")}function mt(){if(!B||!I||!v||!b||!C||!ne||!k){console.error("Required quiz game elements not found for loading question.");return}if(s.quizEnded||s.currentQuestionIndex>=s.currentQuestions.length){Me();return}const e=s.currentQuestions[s.currentQuestionIndex],t=s.questionStates[s.currentQuestionIndex];if(s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],console.log("DEBUG: Current question:",e),console.log("DEBUG: Question field exists:",!!e.question),console.log("DEBUG: Quiz type:",s.currentQuizType),console.log("DEBUG: questionTextElement exists:",!!B),!B&&(console.error("ERROR: questionTextElement is null! Re-caching DOM elements..."),ut(),!B)){console.error("ERROR: questionTextElement still null after re-caching!");return}if(s.currentQuizType==="time-limited")B.style.display="none",console.log("DEBUG: Time-limited quiz - hiding question text");else{if(B.style.display="block",e.question){const a=`Frage ${s.currentQuestionIndex+1}: ${e.question}`;B.textContent=a,console.log("DEBUG: Using original question text:",a)}else{const a=`Frage ${s.currentQuestionIndex+1}: In welchem Land befindet sich das?`;B.textContent=a,console.log("DEBUG: Using fallback question text:",a)}console.log("DEBUG: Final question element text:",B.textContent),console.log("DEBUG: Final question element innerHTML:",B.innerHTML)}I.innerHTML="",v.style.display="none",v.textContent="",ne.textContent=s.currentStreak;const i=t.locked;if(i){if(b.style.display="none",C.style.display="inline-block",k.style.display="inline-block",I.style.pointerEvents="none",t.answered){v.style.display="block";const r=s.userAnswers[s.currentQuestionIndex]===e.correctAnswer;v.textContent=r?"✓ Richtig! Diese Antwort ist gesperrt.":"✗ Falsch. Diese Antwort ist gesperrt.",v.className=r?"feedback correct":"feedback incorrect"}}else b.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I.style.pointerEvents="auto";if(f&&e.image)if(f.src=e.image,f.style.display="block",e.question?f.alt=`Bild zur Frage: ${e.question}`:f.alt=`Bild zur Frage ${s.currentQuestionIndex+1}`,e.streetViewUrl){f.classList.remove("streetview-locked","streetview-unlocked","streetview-unlocking"),f.classList.add("streetview-available"),f.removeEventListener("click",Ie),f.removeEventListener("click",ke);const a=s.questionStates[s.currentQuestionIndex];a&&a.locked?(f.classList.add("streetview-unlocked"),f.style.cursor="pointer",f.title="Klicke hier, um die Street View-Location zu besuchen!",f.addEventListener("click",Ie)):(f.classList.add("streetview-locked"),f.style.cursor="not-allowed",f.title="Beantworte die Frage, um Street View zu entsperren",f.addEventListener("click",ke))}else f.classList.remove("streetview-available","streetview-locked","streetview-unlocked","streetview-unlocking"),f.style.cursor="default",f.title="",f.removeEventListener("click",Ie),f.removeEventListener("click",ke);else f&&(f.style.display="none");Gt([...e.options]).forEach(a=>{const r=document.createElement("div");r.classList.add("answer-option"),r.textContent=a,r.dataset.option=a,i?(r.classList.add("locked"),a===e.correctAnswer?r.classList.add("correct"):a===s.selectedAnswer&&r.classList.add("incorrect"),a===s.selectedAnswer&&r.classList.add("selected")):(a===s.selectedAnswer&&r.classList.add("selected"),r.addEventListener("click",wn)),I.appendChild(r)}),zt()}function wn(e){if(!s.questionStates[s.currentQuestionIndex].locked&&b&&b.style.display!=="none"){const i=e.target.dataset.option;s.selectedAnswer=i,s.userAnswers[s.currentQuestionIndex]=i,document.querySelectorAll("#answer-options .answer-option").forEach(o=>{o.classList.remove("selected")}),e.target.classList.add("selected")}}function Ie(e){e.preventDefault();const t=s.currentQuestions[s.currentQuestionIndex];t&&t.streetViewUrl&&(window.open(t.streetViewUrl,"_blank","noopener,noreferrer"),m("Street View-Location wird geöffnet...","info"))}function ke(e){e.preventDefault(),m("Beantworte zuerst die Frage, um Street View zu entsperren!","warning")}function In(){if(!f)return;const e=s.currentQuestions[s.currentQuestionIndex];e&&e.streetViewUrl&&f.classList.contains("streetview-locked")&&(f.classList.remove("streetview-locked"),f.classList.add("streetview-unlocking"),f.removeEventListener("click",ke),f.style.cursor="pointer",f.title="Klicke hier, um die Street View-Location zu besuchen!",f.addEventListener("click",Ie),setTimeout(()=>{f.classList.remove("streetview-unlocking"),f.classList.add("streetview-unlocked")},600))}function zt(){if(!ce||!U||!k||!b||!C)return;ce.style.display=s.currentQuestionIndex>0?"inline-block":"none",k&&(k.style.display="inline-block"),b.style.display==="none"?(C.style.display=s.currentQuestionIndex<s.currentQuestions.length-1?"inline-block":"none",U.style.display=s.currentQuestionIndex>=s.currentQuestions.length-1?"inline-block":"none"):(C.style.display="none",U.style.display="none")}function kn(e=!1){if(console.log("checkAnswer called with isTimeout:",e,"current score:",s.score,"current streak:",s.currentStreak),b&&b.style.display==="none"&&!e){console.log("Answer already submitted for this question, ignoring duplicate call");return}if(!b||!v||!I||!C||!U||!ne||!k){console.error("Required quiz elements not found for checking answer.");return}if(!e&&!s.selectedAnswer){m("Bitte wähle zuerst eine Antwort aus.","warning");return}clearInterval(s.timerId),s.isTimerRunning=!1,b.style.display="none",I.style.pointerEvents="none";const t=s.currentQuestions[s.currentQuestionIndex],i=!e&&s.selectedAnswer===t.correctAnswer;In(),i?(s.quizEnded||(console.log("Score before increment:",s.score),s.score++,console.log("Score after increment:",s.score),console.log("Streak before increment:",s.currentStreak),s.currentStreak++,console.log("Streak after increment:",s.currentStreak),s.maxStreak=Math.max(s.maxStreak,s.currentStreak),console.log("Max streak updated to:",s.maxStreak)),v.textContent=`Richtig! ${t.explanation||""}`,v.style.backgroundColor="var(--clr-feedback-correct)",Pe("correct")):(e?v.textContent=`Zeit abgelaufen! Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`:v.textContent=`Falsch. Richtig wäre: ${t.correctAnswer}. ${t.explanation||""}`,v.style.backgroundColor="var(--clr-feedback-incorrect)",Pe("incorrect")),ne.textContent=s.currentStreak,document.querySelectorAll("#answer-options .answer-option").forEach(o=>{o.classList.remove("selected"),o.dataset.option===t.correctAnswer?o.classList.add("correct"):o.dataset.option===s.selectedAnswer&&!i&&o.classList.add("incorrect")}),v.style.display="block",s.questionStates[s.currentQuestionIndex].answered=!0,zt()}function Sn(){!C||!b||!v||!k||(s.questionStates[s.currentQuestionIndex]&&(s.questionStates[s.currentQuestionIndex].locked=!0),s.currentQuestionIndex<s.currentQuestions.length-1?(s.currentQuestionIndex++,s.selectedAnswer=null,v.style.display="none",b.style.display="inline-block",C.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),mt(),s.currentQuizType==="time-limited"&&gt()):Me())}function Bn(){clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,!(!ce||!b||!C||!U||!v||!k)&&s.currentQuestionIndex>0&&(s.currentQuestionIndex--,s.selectedAnswer=s.userAnswers[s.currentQuestionIndex],v.style.display="none",b.style.display="inline-block",C.style.display="none",U.style.display="none",k.style.display="inline-block",I&&(I.style.pointerEvents="auto"),mt(),s.currentQuizType==="time-limited"&&gt())}async function Me(){if(console.log("finishQuiz called. Current quiz state:",JSON.stringify(s)),clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,s.quizEnded=!0,h.playerName&&h.playerName!=="Anonym"){const i=s.currentQuizType==="time-limited"?s.selectedTimeLimit:null;await un(h.playerName,s.score,s.currentQuizType,s.currentCategory,s.currentQuestions.length,s.maxStreak,i)}const e=s.currentQuestionIndex<s.currentQuestions.length,t=s.currentQuizType;s.currentCategory,e&&(!x||x.style.display==="none")?(console.log("Finishing mid-quiz, going to category selection."),Ee(),Te(t||"multiple-choice")):(console.log("Quiz ended naturally or from score screen, showing score."),Ln())}function Ln(){if(!x||!je||!Re||!He){console.error("Scoreboard elements not found.");return}const e=E==null?void 0:E.querySelector(".question-container"),t=E==null?void 0:E.querySelector(".quiz-controls");e&&(e.style.display="none"),T&&(T.style.display="none"),v&&(v.style.display="none"),t&&(t.style.display="none"),te&&(te.style.display="none"),je.textContent=`${s.score} / ${s.currentQuestions.length}`,Re.textContent=s.maxStreak;let i="";const o=s.currentQuestions.length>0?s.score/s.currentQuestions.length:0;if(s.maxStreak>=10?i=`Wow, ${s.maxStreak} in Folge! Spitzenleistung!`:s.maxStreak>=5?i=`Starke Serie von ${s.maxStreak}! Gut gemacht!`:o>=.7?i="Sehr gutes Ergebnis!":o>=.5?i="Gut gemacht!":i="Übung macht den Meister! Schau doch mal im Lernbereich vorbei.",He.textContent=i,x.style.display="block",Pe("quizEnd"),x){let a=x.querySelector(".scoreboard-actions");a||(a=document.createElement("div"),a.className="scoreboard-actions",x.appendChild(a)),a.innerHTML="";const r=document.createElement("button");r.textContent="Erneut spielen (Zufall)",r.className="btn",r.id="play-again-random-btn",r.addEventListener("click",()=>{Ke(s.currentQuizType,s.currentCategory)}),a.appendChild(r);const c=document.createElement("button");c.textContent="Fortfahren",c.className="btn",c.id="fortfahren-btn",c.addEventListener("click",()=>{Ke(s.currentQuizType,s.currentCategory)}),a.appendChild(c);const d=document.createElement("button");d.textContent="Quiz beenden",d.className="btn",d.id="scoreboard-main-menu-btn",d.addEventListener("click",()=>{Ee(),j("home")}),a.appendChild(d)}}function gt(){if(!T||!pe||!Ce)return;s.imagePhaseActive=!0,T.style.display="block",T.style.background="var(--clr-primary)",Ce.textContent="Bild sichtbar:";let e=s.selectedTimeLimit;pe.textContent=e.toFixed(1),clearInterval(s.timerId),s.isTimerRunning=!0,f&&(f.style.display="block");const t=e<1?100:1e3,i=e<1?.1:1;s.timerId=setInterval(()=>{e-=i,e<=0?(e=0,clearInterval(s.timerId),s.isTimerRunning=!1,s.imagePhaseActive=!1,f&&(f.style.display="none",m("Bild ausgeblendet - Wähle das Land!","info")),T&&(T.style.display="none")):pe.textContent=e<1?e.toFixed(1):Math.ceil(e)},t)}function qn(e,t){if(l.questions&&l.questions[t])return l.questions[t].length;{let i=e;return e==="time-limited"&&(i="image-based"),!l[i]||!l[i][t]?0:l[i][t].length}}function Cn(e,t,i=null){let o=`terraTueftlerLeaderboard_${e||"default"}_${t||"all"}`;return e==="time-limited"&&i&&(o+=`_${i}s`),o}function $t(e,t="all",i=null){if(!re||!e){re||console.error("Leaderboard list element not found.");return}const o=Mt(e,t,i),a=qn(e,t);if(re.innerHTML="",o.length===0){const r=ft(e),c=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),d=i?` (${i}s)`:"";re.innerHTML=`<li style="text-align: center; color: var(--clr-text-light);">Noch keine Einträge für "${r} - ${c}${d}" vorhanden. Spiel ein Quiz!</li>`;return}o.forEach((r,c)=>{const d=document.createElement("li");d.classList.add("leaderboard-item");const p=a>0&&r.correctAnswers>=a;p&&d.classList.add("perfect-score");const g=document.createElement("span");g.classList.add("leaderboard-rank"),g.textContent=`${c+1}.`;const y=document.createElement("span");y.classList.add("leaderboard-name");const N=p?"⭐ ":"";y.textContent=N+r.name;const A=document.createElement("span");A.classList.add("leaderboard-score");const F=r.totalQuestions>0?Math.round(r.correctAnswers/r.totalQuestions*100):0;A.textContent=`${r.correctAnswers}/${r.totalQuestions} (${F}%)`;const O=document.createElement("span");O.classList.add("leaderboard-streak"),r.maxStreak!==void 0&&(O.textContent=`Serie: ${r.maxStreak}`);const D=document.createElement("span");if(D.classList.add("leaderboard-time"),r.completedAt){const z=new Date(r.completedAt),w=z.toLocaleDateString("de-DE")+" "+z.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});D.textContent=w}else if(r.lastPlayed){const z=new Date(r.lastPlayed),w=z.toLocaleDateString("de-DE")+" "+z.toLocaleTimeString("de-DE",{hour:"2-digit",minute:"2-digit"});D.textContent=w}d.appendChild(g),d.appendChild(y),d.appendChild(A),r.maxStreak!==void 0&&d.appendChild(O),D.textContent&&d.appendChild(D),re.appendChild(d)})}async function Tn(e,t="all",i=null){if(!e)return;const o=ft(e),a=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),r=i?` (${i}s)`:"",c=`${o} - ${a}${r}`;if(confirm(`Möchtest du die Rangliste für "${c}" wirklich löschen?`)){await pn(e,t,i);const d=Cn(e,t,i);localStorage.removeItem(d),$t(e,t,i)}}function ft(e){switch(e){case"multiple-choice":return"Multiple Choice";case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}const S={isInitialized:!1,dragDropInitialized:!1,currentStats:{totalCategories:0,totalQuestions:0,totalModes:3}};let n={},ee=!1;function Nt(){n={adminSection:document.getElementById("admin"),addCategoryBtn:document.getElementById("add-category-btn"),addQuestionBtn:document.getElementById("add-question-btn"),manageContentBtn:document.getElementById("manage-content-btn"),exportDataBtn:document.getElementById("export-data-btn"),importDataBtn:document.getElementById("import-data-btn"),totalCategoriesSpan:document.getElementById("total-categories"),totalQuestionsSpan:document.getElementById("total-questions"),totalModesSpan:document.getElementById("total-modes"),totalLeaderboardEntriesSpan:document.getElementById("total-leaderboard-entries"),totalPlayersSpan:document.getElementById("total-players"),clearLeaderboardBtn:document.getElementById("clear-leaderboard-btn"),leaderboardStatsBtn:document.getElementById("leaderboard-stats-btn"),leaderboardStatsModal:document.getElementById("leaderboard-stats-modal"),closeStatsModal:document.getElementById("close-stats-modal"),closeStatsBtn:document.getElementById("close-stats"),statsTotalEntries:document.getElementById("stats-total-entries"),statsTotalPlayers:document.getElementById("stats-total-players"),statsLastUpdated:document.getElementById("stats-last-updated"),topPerformersList:document.getElementById("top-performers-list"),categoryStats:document.getElementById("category-stats"),addCategoryModal:document.getElementById("add-category-modal"),closeCategoryModal:document.getElementById("close-category-modal"),categoryNameInput:document.getElementById("category-name"),modeMultipleChoice:document.getElementById("mode-multiple-choice"),modeImageBased:document.getElementById("mode-image-based"),modeTimeLimited:document.getElementById("mode-time-limited"),categoryDescriptionInput:document.getElementById("category-description"),cancelCategoryBtn:document.getElementById("cancel-category"),confirmCategoryBtn:document.getElementById("confirm-category"),addQuestionModal:document.getElementById("add-question-modal"),closeQuestionModal:document.getElementById("close-question-modal"),questionModeSelect:document.getElementById("question-mode"),questionCategorySelect:document.getElementById("question-category"),questionTextInput:document.getElementById("admin-question-text"),questionImageInput:document.getElementById("question-image"),questionImageFileInput:document.getElementById("question-image-file"),imagePreview:document.getElementById("image-preview"),previewImg:document.getElementById("preview-img"),imageFilename:document.getElementById("image-filename"),imageSize:document.getElementById("image-size"),option1Input:document.getElementById("option-1"),option2Input:document.getElementById("option-2"),option3Input:document.getElementById("option-3"),option4Input:document.getElementById("option-4"),correctAnswerSelect:document.getElementById("correct-answer"),questionExplanationInput:document.getElementById("question-explanation"),questionStreetViewUrlInput:document.getElementById("question-streetview-url"),questionPreview:document.getElementById("question-preview"),previewQuestionBtn:document.getElementById("preview-question"),cancelQuestionBtn:document.getElementById("cancel-question"),confirmQuestionBtn:document.getElementById("confirm-question"),contentManagementModal:document.getElementById("content-management-modal"),closeContentModal:document.getElementById("close-content-modal"),categoriesTab:document.getElementById("categories-tab"),questionsTab:document.getElementById("questions-tab"),categoriesContent:document.getElementById("categories-content"),questionsContent:document.getElementById("questions-content"),categoriesList:document.getElementById("categories-list"),questionsList:document.getElementById("questions-list"),filterMode:document.getElementById("filter-mode"),filterCategory:document.getElementById("filter-category"),closeContentBtn:document.getElementById("close-content"),deleteConfirmationModal:document.getElementById("delete-confirmation-modal"),closeDeleteModal:document.getElementById("close-delete-modal"),deleteModalTitle:document.getElementById("delete-modal-title"),deleteModalMessage:document.getElementById("delete-modal-message"),deleteModalDetails:document.getElementById("delete-modal-details"),cancelDeleteBtn:document.getElementById("cancel-delete"),confirmDeleteBtn:document.getElementById("confirm-delete"),deleteBtnText:document.getElementById("delete-btn-text"),deleteLoading:document.getElementById("delete-loading")}}function Mn(){S.isInitialized||(console.log("🔧 Initializing admin interface..."),Nt(),console.log("=== ADMIN ELEMENTS DEBUG ==="),Object.keys(n).forEach(e=>{console.log(`${e}:`,n[e]?"✅ Found":"❌ Not found")}),An(),Ft(),Ot(),jt(),Rt(),Vt(),Zn()&&(console.log("Data migration needed - migrating to unified structure..."),Gn().then(e=>{e?(console.log("Data migration completed successfully"),m("Datenstruktur wurde automatisch aktualisiert und optimiert!","success")):(console.error("Data migration failed"),m("Warnung: Datenstruktur-Update fehlgeschlagen","warning")),V()})),V(),S.isInitialized=!0,console.log("Admin interface initialized"))}function An(){n.addCategoryBtn&&n.addCategoryBtn.addEventListener("click",xn),n.addQuestionBtn&&n.addQuestionBtn.addEventListener("click",Qn),n.manageContentBtn&&n.manageContentBtn.addEventListener("click",Wn),n.exportDataBtn&&n.exportDataBtn.addEventListener("click",Hn),n.importDataBtn&&n.importDataBtn.addEventListener("click",Kn),n.clearLeaderboardBtn&&n.clearLeaderboardBtn.addEventListener("click",Vn),n.leaderboardStatsBtn&&n.leaderboardStatsBtn.addEventListener("click",Jn),jt(),Ft(),Ot(),Rt(),Vt()}function Ft(){n.closeCategoryModal&&n.closeCategoryModal.addEventListener("click",Se),n.cancelCategoryBtn&&n.cancelCategoryBtn.addEventListener("click",Se),n.confirmCategoryBtn&&n.confirmCategoryBtn.addEventListener("click",Pn),n.addCategoryModal&&n.addCategoryModal.addEventListener("click",e=>{e.target===n.addCategoryModal&&Se()})}function Ot(){n.closeQuestionModal&&n.closeQuestionModal.addEventListener("click",Be),n.cancelQuestionBtn&&n.cancelQuestionBtn.addEventListener("click",Be),n.confirmQuestionBtn&&n.confirmQuestionBtn.addEventListener("click",jn),n.previewQuestionBtn&&n.previewQuestionBtn.addEventListener("click",Rn),n.questionModeSelect&&n.questionModeSelect.addEventListener("change",Dn),n.questionImageInput&&n.questionImageInput.addEventListener("input",$n),n.questionImageFileInput&&n.questionImageFileInput.addEventListener("change",zn),[n.option1Input,n.option2Input,n.option3Input,n.option4Input].forEach(t=>{t&&t.addEventListener("input",On)}),n.addQuestionModal&&n.addQuestionModal.addEventListener("click",t=>{t.target===n.addQuestionModal&&Be()})}function V(){let e=0,t=0;Object.keys(l).forEach(o=>{const a=Object.keys(l[o]);e+=a.length,a.forEach(r=>{t+=l[o][r].length})});const i=new Set;Object.keys(l).forEach(o=>{Object.keys(l[o]).forEach(a=>{i.add(a)})}),S.currentStats.totalCategories=i.size,S.currentStats.totalQuestions=t;try{const o=xt();S.currentStats.totalLeaderboardEntries=o.totalEntries,S.currentStats.totalPlayers=o.totalPlayers}catch(o){console.error("Error getting leaderboard stats:",o),S.currentStats.totalLeaderboardEntries=0,S.currentStats.totalPlayers=0}n.totalCategoriesSpan&&(n.totalCategoriesSpan.textContent=S.currentStats.totalCategories),n.totalQuestionsSpan&&(n.totalQuestionsSpan.textContent=S.currentStats.totalQuestions),n.totalModesSpan&&(n.totalModesSpan.textContent=S.currentStats.totalModes),n.totalLeaderboardEntriesSpan&&(n.totalLeaderboardEntriesSpan.textContent=S.currentStats.totalLeaderboardEntries),n.totalPlayersSpan&&(n.totalPlayersSpan.textContent=S.currentStats.totalPlayers)}function xn(){n.addCategoryModal&&(Pt(),n.addCategoryModal.style.display="flex",n.categoryNameInput&&n.categoryNameInput.focus())}function Se(){n.addCategoryModal&&(n.addCategoryModal.style.display="none",Pt())}function Pt(){n.categoryNameInput&&(n.categoryNameInput.value=""),n.categoryDescriptionInput&&(n.categoryDescriptionInput.value=""),n.modeMultipleChoice&&(n.modeMultipleChoice.checked=!1),n.modeImageBased&&(n.modeImageBased.checked=!1),n.modeTimeLimited&&(n.modeTimeLimited.checked=!1)}function Qn(){n.addQuestionModal&&(Ut(),n.addQuestionModal.style.display="flex",n.questionModeSelect&&n.questionModeSelect.focus(),S.dragDropInitialized||setTimeout(()=>{Fn(),S.dragDropInitialized=!0},200))}function Be(){n.addQuestionModal&&(n.addQuestionModal.style.display="none",Ut())}function Ut(){ee=!1,n.questionModeSelect&&(n.questionModeSelect.value=""),n.questionCategorySelect&&(n.questionCategorySelect.innerHTML='<option value="">Erst Modus wählen...</option>',n.questionCategorySelect.disabled=!0),n.questionTextInput&&(n.questionTextInput.value=""),n.questionImageInput&&(n.questionImageInput.value=""),n.questionImageFileInput&&(n.questionImageFileInput.value=""),se(),n.option1Input&&(n.option1Input.value=""),n.option2Input&&(n.option2Input.value=""),n.option3Input&&(n.option3Input.value=""),n.option4Input&&(n.option4Input.value=""),n.correctAnswerSelect&&(n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>'),n.questionExplanationInput&&(n.questionExplanationInput.value=""),n.questionStreetViewUrlInput&&(n.questionStreetViewUrlInput.value=""),n.questionPreview&&(n.questionPreview.style.display="none")}function Dn(){if(!n.questionModeSelect||!n.questionCategorySelect)return;const e=n.questionModeSelect.value;if(n.questionCategorySelect.innerHTML="",!e){n.questionCategorySelect.innerHTML='<option value="">Erst Modus wählen...</option>',n.questionCategorySelect.disabled=!0;return}let t=e;if(e==="time-limited"&&(t="image-based"),l[t]){const i=Object.keys(l[t]);console.log("=== CATEGORY DROPDOWN DEBUG ==="),console.log("Selected mode:",e),console.log("Data source:",t),console.log("Available categories:",i),console.log("Quiz data for this source:",l[t]),i.length===0?(n.questionCategorySelect.innerHTML='<option value="">Keine Kategorien verfügbar</option>',n.questionCategorySelect.disabled=!0):(n.questionCategorySelect.innerHTML='<option value="">Kategorie wählen...</option>',i.forEach(o=>{const a=document.createElement("option");a.value=o,a.textContent=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," "),n.questionCategorySelect.appendChild(a)}),n.questionCategorySelect.disabled=!1)}else n.questionCategorySelect.innerHTML='<option value="">Keine Daten für diesen Modus</option>',n.questionCategorySelect.disabled=!0}function zn(e){if(ee){console.log("File upload already in progress, ignoring duplicate event");return}ee=!0;try{const t=e.target.files[0];if(!t){se();return}if(!t.type.startsWith("image/")){m("Bitte wähle eine gültige Bilddatei aus.","warning"),e.target.value="",se();return}if(t.size>10*1024*1024){m("Bilddatei ist zu groß. Maximum: 10MB","warning"),e.target.value="",se();return}n.questionImageInput&&(n.questionImageInput.value="");const i=new FileReader;i.onload=o=>{pt(o.target.result,t.name,t.size)},i.readAsDataURL(t)}finally{setTimeout(()=>{ee=!1},100)}}function $n(){if(!n.questionImageInput||!n.imagePreview||!n.previewImg)return;const e=n.questionImageInput.value.trim();e&&X(e)?(n.questionImageFileInput&&(n.questionImageFileInput.value=""),n.previewImg.src=e,n.previewImg.onload=()=>{pt(e,"Externe URL",null)},n.previewImg.onerror=()=>{se(),m("Bild konnte nicht geladen werden. Überprüfe die URL.","warning")}):se()}function pt(e,t,i){!n.imagePreview||!n.previewImg||(n.previewImg.src=e,n.imagePreview.style.display="block",n.imageFilename&&(n.imageFilename.textContent=t),n.imageSize&&i?n.imageSize.textContent=Nn(i):n.imageSize&&(n.imageSize.textContent=""))}function se(){n.imagePreview&&(n.imagePreview.style.display="none")}function Nn(e){if(e===0)return"0 Bytes";const t=1024,i=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,o)).toFixed(2))+" "+i[o]}function Fn(){const e=document.querySelector(".file-upload-label"),t=n.addQuestionModal;if(console.log("🔧 Setting up drag and drop..."),console.log("File upload label found:",!!e),console.log("Add question modal found:",!!t),!e||!t){console.warn("❌ Drag and drop setup failed - missing elements");return}console.log("✅ Drag and drop setup successful"),["dragenter","dragover","dragleave","drop"].forEach(d=>{document.addEventListener(d,i,!1),t.addEventListener(d,i,!1)}),["dragenter","dragover"].forEach(d=>{e.addEventListener(d,o,!1)}),["dragleave","drop"].forEach(d=>{e.addEventListener(d,a,!1)}),e.addEventListener("drop",r,!1),t.addEventListener("drop",c,!1);function i(d){d.preventDefault(),d.stopPropagation()}function o(d){console.log("🎯 Drag enter/over detected"),e.classList.add("drag-over")}function a(d){console.log("🎯 Drag leave/drop detected"),e.classList.remove("drag-over")}function r(d){if(console.log("📁 Drop event triggered"),ee){console.log("File upload already in progress, ignoring drop event");return}const g=d.dataTransfer.files;if(console.log("Files dropped:",g.length),g.length>0){const y=g[0];if(!y.type.startsWith("image/")){m("Bitte ziehe eine gültige Bilddatei hierher.","warning");return}if(y.size>10*1024*1024){m("Bilddatei ist zu groß. Maximum: 10MB","warning");return}if(ee=!0,n.questionImageFileInput){const N=new DataTransfer;N.items.add(y),n.questionImageFileInput.files=N.files,n.questionImageInput&&(n.questionImageInput.value="");const A=new FileReader;A.onload=F=>{pt(F.target.result,y.name,y.size),setTimeout(()=>{ee=!1},100)},A.readAsDataURL(y)}m("Bild erfolgreich hochgeladen!","success")}}function c(d){d.target.closest(".file-upload-label")||(console.log("📁 Modal drop event triggered"),r(d))}}function On(){var i,o,a,r;if(!n.correctAnswerSelect)return;const e=[(i=n.option1Input)==null?void 0:i.value.trim(),(o=n.option2Input)==null?void 0:o.value.trim(),(a=n.option3Input)==null?void 0:a.value.trim(),(r=n.option4Input)==null?void 0:r.value.trim()].filter(c=>c&&c.length>0),t=n.correctAnswerSelect.value;n.correctAnswerSelect.innerHTML='<option value="">Richtige Antwort wählen...</option>',e.forEach(c=>{const d=document.createElement("option");d.value=c,d.textContent=c,n.correctAnswerSelect.appendChild(d)}),t&&e.includes(t)&&(n.correctAnswerSelect.value=t)}function X(e){try{return new URL(e),!0}catch{return!1}}async function Pn(){var a,r,c,d,p;const e=(a=n.categoryNameInput)==null?void 0:a.value.trim();(r=n.categoryDescriptionInput)==null||r.value.trim();const t=[];if((c=n.modeMultipleChoice)!=null&&c.checked&&t.push("multiple-choice"),(d=n.modeImageBased)!=null&&d.checked&&t.push("image-based"),(p=n.modeTimeLimited)!=null&&p.checked&&t.push("time-limited"),!e){m("Bitte gib einen Kategorie-Namen ein.","warning");return}if(!Un(e)){m("Kategorie-Name darf nur Kleinbuchstaben, Zahlen und Unterstriche enthalten.","warning");return}if(t.length===0){m("Bitte wähle mindestens einen Quiz-Modus aus.","warning");return}const i=[];if(t.forEach(g=>{let y=g;g==="time-limited"&&(y="image-based"),l[y]&&l[y][e]&&i.push(g)}),i.length>0){const g=i.join(", ");m(`Kategorie "${e}" existiert bereits in: ${g}`,"warning");return}t.forEach(g=>{let y=g;g==="time-limited"&&(y="image-based"),l[y]||(l[y]={}),l[y][e]=[]});try{await an(e,t)?console.log("Category saved to backend successfully"):console.warn("Failed to save category to backend, using localStorage backup")}catch(g){console.error("Error saving category to backend:",g)}ue(),V(),Se();const o=t.join(", ");m(`Kategorie "${e}" erfolgreich zu ${o} hinzugefügt!`,"success")}function Un(e){return/^[a-z0-9_]+$/.test(e)}async function jn(e){var N,A,F,O,D,z,w,Qe,De,Et,bt,wt;if(e&&(e.preventDefault(),e.stopPropagation()),n.confirmQuestionBtn){if(n.confirmQuestionBtn.disabled){console.log("Question submission already in progress, ignoring duplicate request");return}n.confirmQuestionBtn.disabled=!0,n.confirmQuestionBtn.textContent="Wird hinzugefügt..."}const t=(N=n.questionModeSelect)==null?void 0:N.value,i=(A=n.questionCategorySelect)==null?void 0:A.value,o=(F=n.questionTextInput)==null?void 0:F.value.trim(),a=(O=n.questionImageInput)==null?void 0:O.value.trim(),r=(D=n.questionImageFileInput)==null?void 0:D.files[0],c=(z=n.questionExplanationInput)==null?void 0:z.value.trim(),d=(w=n.questionStreetViewUrlInput)==null?void 0:w.value.trim(),p=[(Qe=n.option1Input)==null?void 0:Qe.value.trim(),(De=n.option2Input)==null?void 0:De.value.trim(),(Et=n.option3Input)==null?void 0:Et.value.trim(),(bt=n.option4Input)==null?void 0:bt.value.trim()].filter(L=>L&&L.length>0),g=(wt=n.correctAnswerSelect)==null?void 0:wt.value,y=()=>{n.confirmQuestionBtn&&(n.confirmQuestionBtn.disabled=!1,n.confirmQuestionBtn.textContent="Frage hinzufügen")};try{if(console.log("=== QUESTION SUBMISSION DEBUG ==="),console.log("Mode:",t),console.log("Category:",i),console.log("Question Text:",o),console.log("Image URL:",a),console.log("Image File:",r),console.log("Options:",p),console.log("Correct Answer:",g),console.log("Explanation:",c),console.log("Street View URL:",d),!t){m("Bitte wähle einen Quiz-Modus aus.","warning"),y();return}if(!i){m("Bitte wähle eine Kategorie aus.","warning"),y();return}if((t==="image-based"||t==="time-limited")&&!r&&(!a||!X(a))){m("Für bildbasierte und zeitbegrenzte Quizzes ist ein Bild (Upload oder URL) erforderlich.","warning"),y();return}if(t==="multiple-choice"&&!o){m("Für Multiple-Choice-Quizzes ist ein Frage-Text erforderlich.","warning"),y();return}if(p.length<2){m("Bitte gib mindestens 2 Antwortmöglichkeiten ein.","warning"),y();return}if(!g){m("Bitte wähle die richtige Antwort aus.","warning"),y();return}if(!p.includes(g)){m("Die richtige Antwort muss eine der Antwortmöglichkeiten sein.","warning"),y();return}const L={options:p,correctAnswer:g};o&&(L.question=o),c&&(L.explanation=c),d&&X(d)&&(L.streetViewUrl=d),a&&X(a)&&(L.image=a);const G=new FormData;G.append("mode",t),G.append("category",i),G.append("questionData",JSON.stringify(L));const It=Date.now()+"_"+Math.random().toString(36).substring(2,11);G.append("requestId",It),r&&(!a||!X(a))&&(G.append("image",r),console.log("Adding image file to form data:",r.name,"size:",r.size)),console.log("Submitting question with request ID:",It),console.log("Form data contents:");for(let[me,ge]of G.entries())console.log(`  ${me}:`,ge);const Z=await fetch("/api/add-question",{method:"POST",body:G});if(console.log("Response status:",Z.status),console.log("Response ok:",Z.ok),!Z.ok){const me=await Z.text();console.error("Server error response:",me);let ge;try{ge=JSON.parse(me)}catch{ge={error:me||"Unknown server error"}}throw new Error(ge.error||`HTTP ${Z.status}: ${Z.statusText}`)}const we=await Z.json();console.log("Question added successfully:",we),we.imagePath&&(L.image=we.imagePath),l.questions||(l.questions={}),l.questions[i]||(l.questions[i]=[]),l.questions[i].push(L);let oe=t;t==="time-limited"&&(oe="image-based"),l[oe]||(l[oe]={}),l[oe][i]||(l[oe][i]=[]),l[oe][i].push(L),ue(),V(),Be();const en=we.imagePath?" (Bild automatisch organisiert)":"";m(`Frage erfolgreich zu "${i}" (${t}) hinzugefügt!${en}`,"success")}catch(L){console.error("Error adding question:",L),m(`Fehler beim Hinzufügen der Frage: ${L.message}`,"error")}finally{y()}}function Rn(){var p,g,y,N,A,F,O,D,z;if(!n.questionPreview)return;const e=(p=n.questionTextInput)==null?void 0:p.value.trim(),t=(g=n.questionImageInput)==null?void 0:g.value.trim(),i=(y=n.questionImageFileInput)==null?void 0:y.files[0],o=(N=n.questionStreetViewUrlInput)==null?void 0:N.value.trim(),a=[(A=n.option1Input)==null?void 0:A.value.trim(),(F=n.option2Input)==null?void 0:F.value.trim(),(O=n.option3Input)==null?void 0:O.value.trim(),(D=n.option4Input)==null?void 0:D.value.trim()].filter(w=>w&&w.length>0),r=(z=n.correctAnswerSelect)==null?void 0:z.value;let c="";if(e&&(c+=`<div style="margin-bottom: 1rem; font-weight: 600;">${e}</div>`),t&&X(t))c+=`<img src="${t}" alt="Frage-Bild" class="preview-image">`;else if(i){const w=n.previewImg;w&&w.src&&(c+=`<img src="${w.src}" alt="Frage-Bild" class="preview-image">`)}a.length>0&&(c+='<div class="preview-options">',a.forEach(w=>{c+=`<div class="${w===r?"preview-option correct":"preview-option"}">${w}</div>`}),c+="</div>"),o&&X(o)&&(c+=`<div style="margin-top: 1rem; padding: 0.5rem; background: #e8f4fd; border-radius: 4px; font-size: 0.9rem;">
            🌍 Street View verfügbar: Spieler können nach der Antwort die Location besuchen
        </div>`);const d=n.questionPreview.querySelector(".preview-content");d&&(d.innerHTML=c),n.questionPreview.style.display="block"}function Hn(){try{const e=JSON.stringify(l,null,2),t=new Blob([e],{type:"application/json"}),i=document.createElement("a");i.href=URL.createObjectURL(t),i.download=`terraTueftler-quizData-${new Date().toISOString().split("T")[0]}.json`,i.click(),m("Quiz-Daten erfolgreich exportiert!","success")}catch(e){console.error("Export error:",e),m("Fehler beim Exportieren der Daten.","error")}}function Kn(){const e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=t=>{const i=t.target.files[0];if(!i)return;const o=new FileReader;o.onload=a=>{try{const r=JSON.parse(a.target.result);if(!_n(r)){m("Ungültige Datenstruktur. Bitte überprüfe die JSON-Datei.","error");return}confirm("Möchtest du die aktuellen Quiz-Daten durch die importierten Daten ersetzen? Diese Aktion kann nicht rückgängig gemacht werden.")&&(ue(),Object.keys(r).forEach(c=>{l[c]=r[c]}),V(),m("Quiz-Daten erfolgreich importiert!","success"))}catch(r){console.error("Import error:",r),m("Fehler beim Importieren der Daten. Überprüfe das JSON-Format.","error")}},o.readAsText(i)},e.click()}function _n(e){if(typeof e!="object"||e===null)return!1;const t=["multiple-choice","image-based","time-limited"];for(const i in e)if(t.includes(i)){if(typeof e[i]!="object"||e[i]===null)return!1;for(const o in e[i]){if(!Array.isArray(e[i][o]))return!1;for(const a of e[i][o])if(!a.options||!Array.isArray(a.options)||a.options.length<2||!a.correctAnswer||!a.options.includes(a.correctAnswer))return!1}}return!0}function ue(){try{const t={timestamp:new Date().toISOString(),data:l};localStorage.setItem("terraTueftlerQuizDataBackup",JSON.stringify(t)),console.log("Quiz data backup saved to localStorage")}catch(e){console.error("Error saving backup:",e)}}function jt(){n.closeStatsModal&&n.closeStatsModal.addEventListener("click",$e),n.closeStatsBtn&&n.closeStatsBtn.addEventListener("click",$e),n.leaderboardStatsModal&&n.leaderboardStatsModal.addEventListener("click",e=>{e.target===n.leaderboardStatsModal&&$e()})}async function Vn(){if(confirm("Möchtest du ALLE Ranglisten-Daten wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."))try{await fn(),V(),m("Alle Ranglisten-Daten wurden erfolgreich gelöscht!","success")}catch(e){console.error("Clear error:",e),m("Fehler beim Löschen der Ranglisten-Daten.","error")}}async function Jn(){if(n.leaderboardStatsModal)try{const e=xt();if(n.statsTotalEntries&&(n.statsTotalEntries.textContent=e.totalEntries),n.statsTotalPlayers&&(n.statsTotalPlayers.textContent=e.totalPlayers),n.statsLastUpdated){const t=e.lastUpdated?new Date(e.lastUpdated).toLocaleString("de-DE"):"Nie";n.statsLastUpdated.textContent=t}n.topPerformersList&&(n.topPerformersList.innerHTML="",e.topPerformers.length===0?n.topPerformersList.innerHTML="<p>Keine Leistungen vorhanden</p>":e.topPerformers.forEach((t,i)=>{const o=document.createElement("div");o.className="performer-item",o.innerHTML=`
                        <span class="rank">${i+1}.</span>
                        <span class="name">${t.name}</span>
                        <span class="score">${t.correctAnswers}/${t.totalQuestions}</span>
                        <span class="accuracy">${t.accuracy.toFixed(1)}%</span>
                        <span class="mode">${t.mode}</span>
                        <span class="category">${t.category}</span>
                    `,n.topPerformersList.appendChild(o)})),n.categoryStats&&(n.categoryStats.innerHTML="",Object.keys(e.categoryStats).forEach(t=>{const i=document.createElement("div");i.className="mode-stats",i.innerHTML=`<h5>${be(t)}</h5>`,Object.keys(e.categoryStats[t]).forEach(o=>{const a=e.categoryStats[t][o],r=document.createElement("div");r.className="category-stat",r.innerHTML=`
                        <span class="category-name">${o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," ")}</span>
                        <span class="category-count">${a} Einträge</span>
                    `,i.appendChild(r)}),n.categoryStats.appendChild(i)})),n.leaderboardStatsModal.style.display="flex"}catch(e){console.error("Error showing leaderboard stats:",e),m("Fehler beim Laden der Statistiken.","error")}}function $e(){n.leaderboardStatsModal&&(n.leaderboardStatsModal.style.display="none")}function be(e){switch(e){case"multiple-choice":return"Multiple Choice";case"time-limited":return"Zeitbegrenzt";case"image-based":return"Bildbasiert";default:return e}}async function Gn(){if(console.log("Starting data migration to unified structure..."),!l)return console.error("Quiz data not available for migration"),!1;l.questions||(l.questions={});const e=new Map;let t=0,i=0;["image-based","time-limited","multiple-choice"].forEach(a=>{l[a]&&Object.keys(l[a]).forEach(r=>{r!=="all"&&Array.isArray(l[a][r])&&l[a][r].forEach(c=>{const d=`${c.image||"no-image"}_${c.correctAnswer}_${r}`;e.has(d)?i++:(l.questions[r]||(l.questions[r]=[]),l.questions[r].push(c),e.set(d,!0),t++)})})}),console.log(`Migration completed: ${t} questions migrated, ${i} duplicates removed`);try{return await nn(l),ue(),!0}catch(a){return console.error("Error saving migrated data:",a),!1}}function Zn(){if(!l)return!1;if(!l.questions||Object.keys(l.questions).length===0){const e=["image-based","time-limited","multiple-choice"];for(const t of e)if(l[t]&&Object.keys(l[t]).length>0)return!0}return!1}function Wn(){n.contentManagementModal&&(n.contentManagementModal.style.display="flex",Ht(),Kt(),yt(),ht())}function Ne(){n.contentManagementModal&&(n.contentManagementModal.style.display="none")}function Rt(){n.closeContentModal&&n.closeContentModal.addEventListener("click",Ne),n.closeContentBtn&&n.closeContentBtn.addEventListener("click",Ne),n.categoriesTab&&n.categoriesTab.addEventListener("click",Ht),n.questionsTab&&n.questionsTab.addEventListener("click",Xn),n.filterMode&&n.filterMode.addEventListener("change",Bt),n.filterCategory&&n.filterCategory.addEventListener("change",Bt),n.contentManagementModal&&n.contentManagementModal.addEventListener("click",e=>{e.target===n.contentManagementModal&&Ne()})}function Ht(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.categoriesTab.classList.add("active"),n.questionsTab.classList.remove("active"),n.categoriesContent.classList.add("active"),n.questionsContent.classList.remove("active"))}function Xn(){n.categoriesTab&&n.questionsTab&&n.categoriesContent&&n.questionsContent&&(n.questionsTab.classList.add("active"),n.categoriesTab.classList.remove("active"),n.questionsContent.classList.add("active"),n.categoriesContent.classList.remove("active"),ht())}function Kt(){if(!n.categoriesList)return;const e=new Set,t={};if(Object.keys(l).forEach(i=>{Object.keys(l[i]).forEach(o=>{e.add(o),t[o]||(t[o]={modes:[],totalQuestions:0}),t[o].modes.push(i),t[o].totalQuestions+=l[i][o].length})}),n.categoriesList.innerHTML="",e.size===0){n.categoriesList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">📁</div>
                <p>Keine Kategorien vorhanden</p>
            </div>
        `;return}Array.from(e).sort().forEach(i=>{const o=t[i],a=document.createElement("div");a.className="content-item";const r=i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," "),c=o.modes.map(d=>be(d)).join(", ");a.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">${r}</div>
                <div class="content-item-meta">
                    <span>Modi: ${c}</span>
                    <span>${o.totalQuestions} Fragen</span>
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteCategory('${i}')">
                    🗑️ Löschen
                </button>
            </div>
        `,n.categoriesList.appendChild(a)})}function yt(){if(!n.questionsList)return;const e=[];Object.keys(l).forEach(t=>{Object.keys(l[t]).forEach(i=>{l[t][i].forEach((o,a)=>{e.push({mode:t,category:i,index:a,question:o,displayName:i.charAt(0).toUpperCase()+i.slice(1).replace(/_/g," ")})})})}),_t(e)}function _t(e){if(n.questionsList){if(n.questionsList.innerHTML="",e.length===0){n.questionsList.innerHTML=`
            <div class="empty-state">
                <div class="empty-state-icon">❓</div>
                <p>Keine Fragen vorhanden</p>
            </div>
        `;return}e.forEach(t=>{const i=document.createElement("div");i.className="content-item";const o=t.question.question||"Bildbasierte Frage",a=!!t.question.image,r=a?`<img src="${t.question.image}" class="question-preview-mini" alt="Vorschau">`:"";i.innerHTML=`
            <div class="content-item-info">
                <div class="content-item-title">
                    ${r}
                    <span class="question-text-preview">${o}</span>
                </div>
                <div class="content-item-meta">
                    <span>Kategorie: ${t.displayName}</span>
                    <span>Modus: ${be(t.mode)}</span>
                    <span>Antwort: ${t.question.correctAnswer}</span>
                    ${a?"<span>📷 Mit Bild</span>":""}
                </div>
            </div>
            <div class="content-item-actions">
                <button class="btn btn-danger btn-small" onclick="confirmDeleteQuestion('${t.mode}', '${t.category}', ${t.index})">
                    🗑️ Löschen
                </button>
            </div>
        `,n.questionsList.appendChild(i)})}}function ht(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value;n.filterMode.innerHTML=`
        <option value="">Alle Modi</option>
        <option value="multiple-choice">Multiple Choice</option>
        <option value="image-based">Bildbasiert</option>
        <option value="time-limited">Zeitbegrenzt</option>
    `,n.filterMode.value=e;const t=new Set;Object.keys(l).forEach(o=>{Object.keys(l[o]).forEach(a=>{t.add(a)})});const i=n.filterCategory.value;n.filterCategory.innerHTML='<option value="">Alle Kategorien</option>',Array.from(t).sort().forEach(o=>{const a=o.charAt(0).toUpperCase()+o.slice(1).replace(/_/g," "),r=document.createElement("option");r.value=o,r.textContent=a,n.filterCategory.appendChild(r)}),n.filterCategory.value=i}function Bt(){if(!n.filterMode||!n.filterCategory)return;const e=n.filterMode.value,t=n.filterCategory.value,i=[];Object.keys(l).forEach(o=>{e&&o!==e||Object.keys(l[o]).forEach(a=>{t&&a!==t||l[o][a].forEach((r,c)=>{i.push({mode:o,category:a,index:c,question:r,displayName:a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," ")})})})}),_t(i)}let $={type:null,data:null};function Vt(){n.closeDeleteModal&&n.closeDeleteModal.addEventListener("click",Le),n.cancelDeleteBtn&&n.cancelDeleteBtn.addEventListener("click",Le),n.confirmDeleteBtn&&n.confirmDeleteBtn.addEventListener("click",ei),n.deleteConfirmationModal&&n.deleteConfirmationModal.addEventListener("click",e=>{e.target===n.deleteConfirmationModal&&Le()})}function Le(){n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="none",$={type:null,data:null})}window.confirmDeleteCategory=function(e){const t=Yn(e);if($={type:"category",data:{categoryName:e}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Kategorie löschen"),n.deleteModalMessage&&(n.deleteModalMessage.textContent=`Möchtest du die Kategorie "${e}" wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.`),n.deleteModalDetails){const i=e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ");n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Kategorie:</strong> ${i}</li>
                <li><strong>Betroffene Modi:</strong> ${t.modes.map(o=>be(o)).join(", ")}</li>
                <li><strong>Anzahl Fragen:</strong> ${t.totalQuestions}</li>
                <li><strong>Bilder:</strong> ${t.imageCount} Dateien werden gelöscht</li>
                <li><strong>Ordner:</strong> Kategorie-Ordner wird entfernt (falls leer)</li>
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Kategorie löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};window.confirmDeleteQuestion=function(e,t,i){const o=l[e]&&l[e][t]&&l[e][t][i];if(!o){m("Frage nicht gefunden.","error");return}$={type:"question",data:{mode:e,category:t,questionIndex:i}},n.deleteModalTitle&&(n.deleteModalTitle.textContent="Frage löschen");const a=o.question||"Bildbasierte Frage";if(n.deleteModalMessage&&(n.deleteModalMessage.textContent="Möchtest du diese Frage wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden."),n.deleteModalDetails){const r=t.charAt(0).toUpperCase()+t.slice(1).replace(/_/g," "),c=!!o.image;n.deleteModalDetails.innerHTML=`
            <h5>Folgende Daten werden gelöscht:</h5>
            <ul>
                <li><strong>Frage:</strong> ${a}</li>
                <li><strong>Kategorie:</strong> ${r}</li>
                <li><strong>Modus:</strong> ${be(e)}</li>
                <li><strong>Richtige Antwort:</strong> ${o.correctAnswer}</li>
                ${c?`<li><strong>Bild:</strong> ${o.image} wird gelöscht</li>`:""}
            </ul>
        `}n.deleteBtnText&&(n.deleteBtnText.textContent="Frage löschen"),n.deleteConfirmationModal&&(n.deleteConfirmationModal.style.display="flex")};function Yn(e){const t={modes:[],totalQuestions:0,imageCount:0};return Object.keys(l).forEach(i=>{if(l[i][e]){t.modes.push(i);const o=l[i][e];t.totalQuestions+=o.length,o.forEach(a=>{a.image&&a.image.startsWith("assets/images/")&&t.imageCount++})}}),t}async function ei(){if(!(!$.type||!$.data)){n.deleteBtnText&&(n.deleteBtnText.style.display="none"),n.deleteLoading&&(n.deleteLoading.style.display="inline-block"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!0);try{$.type==="category"?await ti($.data.categoryName):$.type==="question"&&await ni($.data.mode,$.data.category,$.data.questionIndex)}catch(e){console.error("Deletion error:",e),m(`Fehler beim Löschen: ${e.message}`,"error")}finally{n.deleteBtnText&&(n.deleteBtnText.style.display="inline"),n.deleteLoading&&(n.deleteLoading.style.display="none"),n.confirmDeleteBtn&&(n.confirmDeleteBtn.disabled=!1),Le()}}}async function ti(e){try{const t=await cn(e);Object.keys(l).forEach(o=>{l[o][e]&&delete l[o][e]}),ue(),V(),Kt(),yt(),ht();const i=t.deletedQuestions>0?` (${t.deletedQuestions} Fragen, ${t.deletedImages.length} Bilder gelöscht)`:"";m(`Kategorie "${e}" erfolgreich gelöscht!${i}`,"success")}catch(t){throw console.error("Error deleting category:",t),new Error(`Kategorie konnte nicht gelöscht werden: ${t.message}`)}}async function ni(e,t,i){try{const o=await ln(e,t,i);let a=e;e==="time-limited"&&(a="image-based"),l[a]&&l[a][t]&&l[a][t].splice(i,1),ue(),V(),yt();const r=o.deletedImagePath?" (Bild gelöscht)":"";m(`Frage erfolgreich gelöscht!${r}`,"success")}catch(o){throw console.error("Error deleting question:",o),new Error(`Frage konnte nicht gelöscht werden: ${o.message}`)}}const h={currentSection:"home",playerName:"Anonym"};let le,P,vt,Jt,H,_e,Ve,ve,W,Ae,ye,ie,q,M,Q,Y,qe,Je,Ge,Ze,We,K,_,Xe,Ye,et,tt,nt,it,ot,at,rt,st,lt;function ii(){le=document.getElementById("nav-links"),P=document.getElementById("burger"),vt=document.querySelectorAll("main > section"),Jt=document.querySelectorAll(".nav-links a[data-target]"),document.querySelectorAll(".btn[data-target]"),document.getElementById("player-name-area"),H=document.getElementById("player-name"),_e=document.getElementById("save-player-name"),Ve=document.getElementById("current-player-name"),document.getElementById("learn"),document.getElementById("learn-overview"),ve=document.getElementById("learn-category-select"),W=document.getElementById("learn-content-grid"),Ae=document.getElementById("sound-toggle"),ye=document.getElementById("theme-select"),ie=document.getElementById("time-limit-select"),q=document.getElementById("time-limit-options"),M=document.getElementById("leaderboard-mode-select"),Q=document.getElementById("leaderboard-category-select"),Y=document.getElementById("leaderboard-time-select"),qe=document.getElementById("time-limit-filter"),Je=document.getElementById("refresh-leaderboard"),Ge=document.getElementById("clear-leaderboard"),Ze=document.getElementById("current-player-display"),We=document.getElementById("change-player-btn"),K=document.getElementById("player-name-modal"),_=document.getElementById("new-player-name"),Xe=document.getElementById("close-player-modal"),Ye=document.getElementById("cancel-player-change"),et=document.getElementById("confirm-player-change"),tt=document.querySelectorAll("#quiz .quiz-option[data-quiz-type]"),lt=document.getElementById("quiz-category-options"),nt=document.getElementById("back-to-quiz-selection"),it=document.getElementById("submit-answer"),ot=document.getElementById("next-question"),at=document.getElementById("prev-question"),rt=document.getElementById("quit-quiz"),st=document.getElementById("finish-quiz")}function Gt(e){for(let t=e.length-1;t>0;t--){const i=Math.floor(Math.random()*(t+1));[e[t],e[i]]=[e[i],e[t]]}return e}function m(e,t="info"){const i=document.createElement("div");switch(i.textContent=e,i.style.position="fixed",i.style.bottom="20px",i.style.left="50%",i.style.transform="translateX(-50%)",i.style.padding="10px 20px",i.style.borderRadius="var(--radius)",i.style.color="#fff",i.style.zIndex="2000",i.style.boxShadow="var(--shadow-lg)",i.style.textAlign="center",i.style.opacity="0",i.style.transition="opacity 0.5s ease",t){case"warning":i.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"error":i.style.backgroundColor="var(--clr-feedback-incorrect)";break;case"info":default:i.style.backgroundColor="var(--clr-secondary)";break}document.body.appendChild(i),requestAnimationFrame(()=>{i.style.opacity="1"}),setTimeout(()=>{i.style.opacity="0",setTimeout(()=>{i.parentNode&&i.remove()},500)},3e3)}function j(e){if(document.getElementById(e)||(console.error(`Section with ID "${e}" not found. Defaulting to home.`),e="home"),h.currentSection=e,vt.forEach(i=>{const o=i.id===e;i.style.display=o?"block":"none"}),Jt.forEach(i=>{i.classList.toggle("active",i.dataset.target===e)}),le&&P&&le.classList.contains("show")&&(le.classList.remove("show"),P.classList.remove("change"),P.setAttribute("aria-expanded","false")),e!=="quiz-game"&&e!=="quiz-category"&&Ee(),e!=="quiz"&&q){q.style.display="none";const i=document.getElementById("start-time-limited-quiz"),o=document.getElementById("time-limit-instruction");i&&(i.style.display="none"),o&&(o.style.display="none")}e==="learn"&&(Yt("all"),ve&&(ve.value="all")),e==="leaderboard"&&oi(),e==="admin"&&Mn(),e==="quiz"&&ct(),window.scrollTo(0,0)}function oi(){!M||!Q||(Zt(),Wt(),fe())}function Zt(){if(!M||!Q)return;const e=M.value;Q.innerHTML="";let t=e;if(e==="time-limited"&&(t="image-based"),!l[t])return;const i=Object.keys(l[t]);let o;e==="multiple-choice"?o=i.filter(a=>["all","architecture","straßenschilder"].includes(a)):e==="time-limited"?o=i.filter(a=>["all","landschaft","städte_erkennen","wahrzeichen","geographie_extrem"].includes(a)):o=i,o.forEach(a=>{const r=document.createElement("option");r.value=a,r.textContent=a.charAt(0).toUpperCase()+a.slice(1).replace(/_/g," "),Q.appendChild(r)})}function Wt(){if(!qe||!M)return;M.value==="time-limited"?qe.style.display="block":qe.style.display="none"}function fe(){if(!M||!Q)return;const e=M.value,t=Q.value,i=e==="time-limited"&&Y?parseFloat(Y.value):null;$t(e,t,i)}function ct(){Ze&&(Ze.textContent=`Aktueller Spieler: ${h.playerName}`)}function ai(){K&&_&&(_.value=h.playerName==="Anonym"?"":h.playerName,K.style.display="flex",_.focus())}function he(){K&&(K.style.display="none")}function Lt(){if(!_)return;const e=_.value.trim();e&&e.length<=20?(h.playerName=e,localStorage.setItem("terraTueftlerPlayerName",e),de(),ct(),he(),m(`Spieler geändert zu: ${e}`,"info")):e.length>20?m("Name darf maximal 20 Zeichen haben.","warning"):(h.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName","Anonym"),de(),ct(),he(),m('Spieler auf "Anonym" zurückgesetzt.',"info"))}function de(){H&&(H.value=h.playerName),Ve&&(Ve.textContent=`Aktueller Spieler: ${h.playerName}`)}function qt(){if(!H)return;const e=H.value.trim();e&&e.length>0&&e.length<=20?(h.playerName=e,localStorage.setItem("terraTueftlerPlayerName",h.playerName),de(),m("Name gespeichert!","info")):(m("Bitte gib einen gültigen Namen ein (1-20 Zeichen).","warning"),H.value=h.playerName)}function Xt(){if(!h.playerName||h.playerName==="Anonym"){const e=prompt("Bitte gib deinen Spielernamen für die Rangliste ein (max. 20 Zeichen):",h.playerName!=="Anonym"?h.playerName:"");return e&&e.trim()!==""&&e.trim().length<=20?(h.playerName=e.trim(),localStorage.setItem("terraTueftlerPlayerName",h.playerName),de(),!0):e!==null?(alert("Ungültiger Name. Bitte 1-20 Zeichen verwenden."),Xt()):(localStorage.getItem("terraTueftlerPlayerName")||(h.playerName="Anonym",localStorage.setItem("terraTueftlerPlayerName",h.playerName),de()),!1)}return!0}function ri(){if(s.currentQuizType="time-limited",q){q.style.display="block";let e=document.getElementById("time-limit-instruction");if(e)e.style.display="block";else{e=document.createElement("p"),e.id="time-limit-instruction",e.textContent="Wähle dein gewünschtes Zeitlimit pro Frage:",e.style.marginBottom="0.5rem",e.style.fontWeight="600";const i=q.querySelector("select");i?q.insertBefore(e,i):q.appendChild(e)}let t=document.getElementById("start-time-limited-quiz");t?t.style.display="block":(t=document.createElement("button"),t.id="start-time-limited-quiz",t.className="btn",t.textContent="Quiz starten",t.style.marginTop="1rem",t.style.display="block",t.style.width="100%",t.addEventListener("click",()=>{ie&&(s.selectedTimeLimit=parseFloat(ie.value)||1),q.style.display="none",Te("time-limited")}),q.appendChild(t))}else console.error("Time limit options div not found"),Te("time-limited")}function Yt(e="all"){if(!W){console.warn("Learning content grid not found");return}if(!l){console.error("Quiz data not available for learning content"),W.innerHTML='<p class="no-content">Lerndaten konnten nicht geladen werden.</p>';return}let t=[];try{if(l.questions)e==="all"?Object.keys(l.questions).forEach(o=>{if(o!=="all"&&l.questions[o]){const a=l.questions[o];Array.isArray(a)&&a.forEach(r=>{r.image&&t.push({...r,category:o,sourceType:"unified"})})}}):l.questions[e]&&Array.isArray(l.questions[e])&&l.questions[e].forEach(o=>{o.image&&t.push({...o,category:e,sourceType:"unified"})});else{const o=["image-based"],a=new Set;e==="all"?o.forEach(r=>{l[r]&&Object.keys(l[r]).forEach(c=>{if(c!=="all"&&l[r][c]){const d=l[r][c];Array.isArray(d)&&d.forEach(p=>{p.image&&!a.has(p.image)&&(a.add(p.image),t.push({...p,category:c,sourceType:r}))})}})}):o.forEach(r=>{l[r]&&l[r][e]&&Array.isArray(l[r][e])&&l[r][e].forEach(c=>{c.image&&!a.has(c.image)&&(a.add(c.image),t.push({...c,category:e,sourceType:r}))})})}}catch(o){console.error("Error processing learning content:",o),W.innerHTML='<p class="no-content">Fehler beim Laden der Lerninhalte.</p>';return}if(W.innerHTML="",t.length===0){W.innerHTML='<p class="no-content">Keine Lerninhalte für diese Kategorie verfügbar.</p>';return}console.log(`Learning content loaded: ${t.length} items from category "${e}"`);const i=t.reduce((o,a)=>(o[a.sourceType]=(o[a.sourceType]||0)+1,o),{});console.log("Content by source:",i),t.sort((o,a)=>o.category!==a.category?o.category.localeCompare(a.category):o.correctAnswer.localeCompare(a.correctAnswer)),t.forEach(o=>{const a=si(o);W.appendChild(a)})}function si(e){const t=document.createElement("div");t.className="learn-reference-item";const i=e.image||"https://placehold.co/400x200/bdc3c7/2c3e50?text=Kein+Bild",o=e.category.charAt(0).toUpperCase()+e.category.slice(1).replace(/_/g," "),a=e.sourceType?`<span class="learn-reference-source" title="Quelle: ${e.sourceType}">${li(e.sourceType)}</span>`:"";return t.innerHTML=`
        <img src="${i}" alt="Lerninhalt" class="learn-reference-image" loading="lazy">
        <div class="learn-reference-content">
            <div class="learn-reference-answer">${e.correctAnswer}</div>
            <div class="learn-reference-explanation">${e.explanation||"Keine zusätzlichen Informationen verfügbar."}</div>
            <div class="learn-reference-meta">
                <div class="learn-reference-category">${o}</div>
                ${a}
            </div>
        </div>
    `,t}function li(e){switch(e){case"image-based":return"Bildrätsel";case"multiple-choice":return"Multiple Choice";case"time-limited":return"Zeitlimit";default:return e}}let Ct=!1;function ci(){if(Ct){console.log("Event listeners already set up, skipping duplicate setup");return}console.log("Setting up event listeners"),Ct=!0,P&&le&&P.addEventListener("click",()=>{const e=P.getAttribute("aria-expanded")==="true";P.setAttribute("aria-expanded",String(!e)),le.classList.toggle("show"),P.classList.toggle("change")}),document.querySelectorAll(".nav-links a[data-target], .btn[data-target]").forEach(e=>{e.addEventListener("click",t=>{t.preventDefault();const i=e.dataset.target;i&&j(i)})}),_e&&H&&(_e.addEventListener("click",qt),H.addEventListener("keypress",e=>{e.key==="Enter"&&qt()})),tt&&tt.forEach(e=>{e.addEventListener("click",()=>{const t=e.dataset.quizType;if(t){if(!l||Object.keys(l).length===0){m("Quiz-Daten werden noch geladen. Bitte warten Sie einen Moment.","warning");return}if(Xt())if(t==="time-limited")ri();else{if(q){q.style.display="none";const i=document.getElementById("start-time-limited-quiz"),o=document.getElementById("time-limit-instruction");i&&(i.style.display="none"),o&&(o.style.display="none")}Te(t)}}})}),ie&&ie.addEventListener("change",e=>{s.selectedTimeLimit=parseFloat(e.target.value)||1}),lt&&lt.addEventListener("click",e=>{const t=e.target.closest(".quiz-option[data-category]");if(t){const i=t.dataset.category;s.currentQuizType&&i&&Ke(s.currentQuizType,i)}}),nt&&nt.addEventListener("click",()=>{if(Ee(),q){q.style.display="none";const e=document.getElementById("start-time-limited-quiz"),t=document.getElementById("time-limit-instruction");e&&(e.style.display="none"),t&&(t.style.display="none")}j("quiz")}),it&&(console.log("Adding event listener to submit button"),it.addEventListener("click",()=>{console.log("Submit button clicked, calling checkAnswer"),kn(!1)})),ot&&ot.addEventListener("click",Sn),at&&at.addEventListener("click",Bn),st&&st.addEventListener("click",Me),rt&&rt.addEventListener("click",Me),ve&&ve.addEventListener("change",e=>{const t=e.target.value;Yt(t)}),M&&M.addEventListener("change",()=>{Zt(),Wt(),fe()}),Q&&Q.addEventListener("change",()=>{fe()}),Y&&Y.addEventListener("change",()=>{fe()}),Je&&Je.addEventListener("click",()=>{fe()}),Ge&&Ge.addEventListener("click",()=>{const e=M==null?void 0:M.value,t=(Q==null?void 0:Q.value)||"all",i=e==="time-limited"&&Y?parseFloat(Y.value):null;Tn(e,t,i)}),We&&We.addEventListener("click",ai),Xe&&Xe.addEventListener("click",he),Ye&&Ye.addEventListener("click",he),et&&et.addEventListener("click",Lt),_&&_.addEventListener("keypress",e=>{e.key==="Enter"&&Lt()}),K&&K.addEventListener("click",e=>{e.target===K&&he()}),Ae&&Ae.addEventListener("change",e=>{bn(e.target.checked)}),ye&&ye.addEventListener("change",e=>{Dt(e.target.value,ye)})}document.addEventListener("DOMContentLoaded",async()=>{console.log("DOM fully loaded and parsed."),await vn(),console.log("Data initialized successfully"),ii(),ut(),Nt(),h.playerName=localStorage.getItem("terraTueftlerPlayerName")||"Anonym",de(),En(ye,Ae,ci);const e=window.location.hash.substring(1)||"home",t=Array.from(vt).map(i=>i.id);j(t.includes(e)?e:"home"),ie&&(ie.value=String(s.selectedTimeLimit)),console.log("TerraTüftler App Initialized.")});
