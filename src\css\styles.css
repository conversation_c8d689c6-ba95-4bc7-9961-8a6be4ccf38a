/* Import other CSS files if needed, e.g., fonts */
@import url('https://fonts.googleapis.com/css2?family=Segoe+UI:wght@400;600;700&display=swap');

/* ===========================
   CSS Variables & Base Styles
   =========================== */
:root { /* Standard Theme (Light) */
    /* Typography */
    --font-base: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --line-height: 1.6;

    /* Layout & Spacing */
    --radius: 0.5rem; /* 8px */
    --transition: 0.3s ease-in-out;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.08);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --max-width: 1200px;
    --container-padding: 1rem;
    --section-margin-bottom: 2rem;

    /* Light Theme Colors */
    --clr-header-bg: #2c3e50;
    --clr-footer-bg: #34495e;
    --clr-primary: #2c3e50;   /* Main primary color */
    --clr-secondary: #3498db; /* Accent blue */
    --clr-accent: #27ae60;   /* Accent green */
    --clr-bg: #ecf0f1;       /* Page background */
    --clr-card: #ffffff;     /* Card background */
    --clr-text: #2c3e50;     /* Default text on light backgrounds */
    --clr-text-light: #555;  /* Lighter text on light backgrounds */
    --clr-text-on-dark: #ffffff; /* Text on dark backgrounds (header, footer, buttons) */
    --clr-header-text: #ffffff; /* Header text color */
    --clr-footer-text: #ffffff; /* Footer text color */
    --clr-button-text: #ffffff; /* Button text color */
    --clr-border: #dce0e1;
    --clr-input-bg: #ffffff;
    --clr-input-text: #2c3e50;
    --clr-feedback-correct: var(--clr-accent);
    --clr-feedback-incorrect: #e74c3c;
    --clr-feedback-text: #ffffff;
    --clr-button-back: #7f8c8d;
    --clr-button-disabled-bg: #bdc3c7;
    --clr-button-disabled-text: #7f8c8d;
    --clr-gold: #ffd700;
    --clr-silver: #c0c0c0;
    --clr-bronze: #cd7f32;
}

/* Global Resets and Box Sizing */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

/* Body Styling */
body {
    font-family: var(--font-base, 'Segoe UI', sans-serif);
    line-height: var(--line-height, 1.6);
    background: var(--clr-bg, #ecf0f1);
    color: var(--clr-text, #2c3e50);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background var(--transition, 0.3s), color var(--transition, 0.3s);
}

/* Basic Element Styling */
a {
    text-decoration: none;
    color: var(--clr-secondary, #3498db);
    transition: color var(--transition, 0.3s);
}
a:hover {
    color: var(--clr-primary, #2c3e50);
}
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Accessibility Focus Styles */
:focus-visible {
    outline: 3px solid var(--clr-secondary);
    outline-offset: 2px;
    border-radius: var(--radius);
}
/* Remove default outline when :focus-visible is supported */
*:focus:not(:focus-visible) {
    outline: none;
}

/* Layout Utils */
main {
    flex: 1 0 auto; /* Ensure main content takes available space and doesn't shrink */
    padding-top: 2rem;
    padding-bottom: 2rem;
}
.container {
    max-width: var(--max-width, 1200px);
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Section Title Styling */
.container > h2:first-of-type { /* Style for main headings within containers */
    text-align: center;
    margin-bottom: 2rem;
    color: var(--clr-primary);
    opacity: 1;
    visibility: visible;
}

/* Headings */
h1, h2, h3, h4 {
    margin-bottom: 1rem;
    line-height: 1.2;
    color: var(--clr-text);
    opacity: 1;
    visibility: visible;
}
h1 { font-size: 2.5rem; }
h2 { font-size: 1.8rem; }
h3 { font-size: 1.4rem; }
h4 { font-size: 1.1rem; }

/* Paragraphs */
p {
    margin-bottom: 1rem;
    color: var(--clr-text-light, #555);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Footer */
footer {
    background: var(--clr-footer-bg);
    color: var(--clr-footer-text);
    text-align: center;
    padding: 1.5rem 1rem;
    margin-top: auto; /* Push footer to bottom */
    border-top: 3px solid var(--clr-secondary);
    transition: background var(--transition), border-color var(--transition);
}

footer p {
    margin-bottom: 0;
    color: inherit;
    font-size: 0.9rem;
}

/* ===========================
   Layout & General Styles
   =========================== */
main > section {
    display: none; /* Hide all sections by default */
    padding-top: 1rem; /* Add padding to sections */
    padding-bottom: 1rem;
}

main > section.active {
    display: block;
    animation: fadeIn 0.6s ease-in-out both;
}

/* ===========================
   Header & Nav Styles
   =========================== */
/* Main navigation header only - not section headers */
body > header,
header:not(.quiz-header):not(.learn-header):not(.leaderboard-header):not(.admin-header) {
    background: var(--clr-header-bg);
    color: var(--clr-header-text);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    transition: background var(--transition), color var(--transition);
    /* Ensure header is always visible */
    opacity: 1;
    visibility: visible;
}

nav {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--clr-header-text); /* Use explicit header text color */
}

.logo svg {
    /* Inherit color from parent (header text color) */
    fill: currentColor;
    margin-right: 0.5rem;
    /* Remove background/padding needed for img */
    background: none;
    padding: 0;
    border-radius: 0; /* Ensure no radius from img style */
    vertical-align: middle; /* Align SVG better with text */
}

.logo span {
    color: var(--clr-header-text);
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 1.5rem;
}

.nav-links a {
    color: var(--clr-header-text);
    padding: 0.5rem 1rem;
    border-radius: var(--radius);
    transition: background var(--transition), transform var(--transition), color var(--transition);
    font-weight: 600;
    position: relative; /* For potential active indicator */
    display: inline-block;
    text-decoration: none;
}

.nav-links a:hover,
.nav-links a.active {
    background: var(--clr-secondary);
    transform: translateY(-2px);
    color: var(--clr-header-text);
}

/* Burger Menu */
.burger {
    display: none; /* Hidden by default, shown in media query */
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 25px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 1100;
    padding: 0;
    position: relative;
}

.burger .bar {
    width: 100%;
    height: 3px;
    background: var(--clr-header-text);
    border-radius: 2px;
    transition: all 0.3s ease-in-out;
    transform-origin: center;
}

.burger.change .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

.burger.change .bar:nth-child(2) {
    opacity: 0;
    transform: scaleX(0);
}

.burger.change .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

/* ===========================
   Button Styles
   =========================== */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--clr-accent);
    color: var(--clr-button-text);
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: background var(--transition), transform var(--transition), box-shadow var(--transition), filter var(--transition);
    text-align: center;
    text-decoration: none; /* Ensure links styled as buttons look right */
}

.btn:hover {
    filter: brightness(0.9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--clr-secondary);
}

.btn-back {
    background: #7f8c8d; /* Default back button color */
}

.btn-quit {
    background: var(--clr-feedback-incorrect);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--clr-border);
    box-shadow: none;
    transform: none;
    filter: grayscale(50%);
}

/* Overrides for disabled state in specific themes are in themes.css */

/* ===========================
   Hero Section
   =========================== */
.hero {
    position: relative;
    padding: 4rem 1rem;
    text-align: center;
    color: #fff; /* Default text color for hero */
    border-radius: 0 0 var(--radius) var(--radius);
    /* Earth panorama background with lighter overlay for vibrant visibility */
    background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.25)),
                url('../assets/images/design/4k-earth-panorama-8l3t9nkefmdx7jfo.jpg') center/cover no-repeat;
    margin-bottom: 2rem;
    overflow: hidden; /* Contain background */
    min-height: 400px; /* Ensure adequate height for the panorama */
    background-attachment: fixed; /* Parallax effect on desktop */
}

/* Additional overlay for enhanced text readability - much lighter for vibrant background */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.2) 70%);
    z-index: 1;
}

/* Ensure hero content appears above the overlay */
.hero > * {
    position: relative;
    z-index: 2;
}

/* Subtle fade-in animation for hero content */
.hero h1,
.hero p,
.hero .btn {
    animation: heroFadeIn 1.2s ease-out forwards;
    opacity: 0;
}

.hero h1 {
    animation-delay: 0.3s;
}

.hero p {
    animation-delay: 0.6s;
}

.hero .btn {
    animation-delay: 0.9s;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced hero button styling */
.hero .btn {
    background: var(--clr-primary);
    border: 2px solid var(--clr-primary);
    color: var(--clr-button-text);
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.hero .btn:hover {
    background: transparent;
    border-color: var(--clr-primary);
    color: var(--clr-primary);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
}

.hero h1 {
    font-size: 2.5rem;
    color: #fff;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9),
                 0 0 15px rgba(0, 0, 0, 0.7),
                 1px 1px 3px rgba(0, 0, 0, 1);
    font-weight: 700;
    letter-spacing: 0.5px;
}

.hero p {
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto 2rem auto;
    color: rgba(255, 255, 255, 0.98);
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.9),
                 0 0 12px rgba(0, 0, 0, 0.6),
                 0.5px 0.5px 2px rgba(0, 0, 0, 1);
    font-weight: 500;
    line-height: 1.6;
}

/* ===========================
   Features Section
   =========================== */
.features {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
}

.feature-card {
    background: var(--clr-card);
    padding: 2rem;
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition), box-shadow var(--transition);
    border: 1px solid var(--clr-border);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-card h3 {
    color: var(--clr-secondary);
    margin-top: 0; /* Remove default margin if needed */
}

.feature-card p {
    color: var(--clr-text-light);
    margin-bottom: 1.5rem;
}

/* Theme-specific feature card styles are in themes.css */

/* ===========================
   Container Styles (Quiz, Learn, Settings, Leaderboard)
   =========================== */
.quiz-container,
.learn-container,
.settings-container,
.leaderboard-container {
    background: var(--clr-card);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
    border: 1px solid var(--clr-border);
}

.quiz-header,
.learn-header,
.leaderboard-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--clr-border);
}

.quiz-header h2,
.learn-header h2,
.leaderboard-header h2 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
}

.quiz-header p,
.learn-header p,
.leaderboard-header p {
    color: var(--clr-text-light);
    margin-top: 0;
    margin-bottom: 1rem; /* Add some space below paragraph */
}

/* ===========================
   Quiz Specific Styles
   =========================== */

/* Player Info Section */
.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--clr-bg);
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
}

#current-player-display {
    font-weight: 600;
    color: var(--clr-text);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Quiz Mode/Category Options */
.quiz-options {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.quiz-option {
    background: var(--clr-secondary);
    color: var(--clr-button-text);
    padding: 1.5rem;
    border-radius: var(--radius);
    text-align: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition), box-shadow var(--transition), filter var(--transition), border-color var(--transition);
    border: 2px solid transparent;
}

.quiz-option:hover {
    filter: brightness(0.9);
    transform: scale(1.03);
    box-shadow: var(--shadow-md);
    border-color: var(--clr-primary);
}

.quiz-option h3 {
    color: inherit;
    margin-bottom: 0.5rem;
}

.quiz-option p {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Theme-specific quiz option styles are in themes.css */

/* Time Limit Selection */
.time-limit-selection {
    margin-top: 1rem; /* Adjusted margin */
    padding-top: 1rem;
    /* border-top: 1px solid var(--clr-border); */
    text-align: center;
}

.time-limit-selection label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: var(--clr-text);
}

.time-limit-selection select {
    padding: 0.5rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    cursor: pointer;
}

/* Player Name Input */
#player-name-area {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--clr-border);
    text-align: center;
}

#player-name-area label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--clr-text);
}

#player-name-area input[type="text"] {
    padding: 0.6rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
    margin-right: 0.5rem;
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    min-width: 200px;
}

#player-name-area button {
    padding: 0.6rem 1rem; /* Match input padding */
    /* Use default .btn styles */
}

#current-player-name {
    margin-top: 1rem;
    color: var(--clr-text-light);
    font-style: italic;
}

/* Quiz Game Area */
.question-container h3#question-text {
    color: var(--clr-primary);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.question-image {
    max-width: 100%;
    max-height: 350px; /* Increased max height */
    width: auto;
    height: auto;
    object-fit: contain; /* Ensure image aspect ratio is maintained */
    border-radius: var(--radius);
    margin: 0 auto 1.5rem auto;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--clr-border);
    background-color: rgba(128, 128, 128, 0.1); /* Light bg for loading */
}

/* Answer Options */
.answer-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Slightly larger min width */
    gap: 1rem;
    margin: 2rem 0;
}

.answer-option {
    background: var(--clr-card);
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    border: 2px solid var(--clr-border);
    cursor: pointer;
    text-align: center;
    font-weight: 600;
    transition: background var(--transition), transform var(--transition), border-color var(--transition), color var(--transition);
    color: var(--clr-text);
}

.answer-option:hover {
    background: var(--clr-secondary);
    color: var(--clr-button-text);
    border-color: var(--clr-secondary);
    transform: translateY(-2px);
}

.answer-option.selected {
    background-color: var(--clr-secondary);
    color: var(--clr-button-text);
    border-color: var(--clr-primary);
    box-shadow: var(--shadow-md);
}

.answer-option.correct {
    background-color: var(--clr-feedback-correct);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-correct);
    animation: pulse 0.5s ease-in-out;
}

.answer-option.incorrect {
    background-color: var(--clr-feedback-incorrect);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-incorrect);
    animation: shake 0.5s ease-in-out;
}

.answer-option.correct:hover,
.answer-option.incorrect:hover {
    transform: none; /* Prevent hover effect on feedback states */
    filter: none;
}

/* ===========================
   Locked Answer Option Styles (Anti-Cheating)
   =========================== */
.answer-option.locked {
    opacity: 0.7;
    cursor: not-allowed;
    position: relative;
    pointer-events: none;
}

.answer-option.locked::after {
    content: '🔒';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    font-size: 0.8rem;
    opacity: 0.6;
}

.answer-option.locked:hover {
    transform: none;
    box-shadow: var(--shadow-sm);
    filter: none;
}

.answer-option.locked.correct {
    background-color: var(--clr-feedback-correct);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-correct);
    opacity: 0.8;
}

.answer-option.locked.incorrect {
    background-color: var(--clr-feedback-incorrect);
    color: var(--clr-feedback-text);
    border-color: var(--clr-feedback-incorrect);
    opacity: 0.8;
}

.answer-option.locked.selected {
    font-weight: 600;
    border-width: 2px;
}

/* Feedback styling for locked questions */
.feedback.correct {
    background-color: var(--clr-feedback-correct);
    color: var(--clr-feedback-text);
    border: 1px solid var(--clr-feedback-correct);
    padding: 1rem;
    border-radius: var(--radius);
    margin-top: 1rem;
}

.feedback.incorrect {
    background-color: var(--clr-feedback-incorrect);
    color: var(--clr-feedback-text);
    border: 1px solid var(--clr-feedback-incorrect);
    padding: 1rem;
    border-radius: var(--radius);
    margin-top: 1rem;
}

/* Feedback, Timer, Streak */
#feedback {
    padding: 1rem;
    border-radius: var(--radius);
    color: var(--clr-feedback-text);
    margin: 1.5rem 0;
    display: none;
    animation: fadeIn 0.5s both;
    text-align: center;
    font-weight: 600;
    line-height: 1.4;
}

.timer {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: none; /* Controlled by JS */
    animation: fadeIn 0.5s both;
    text-align: center;
    color: var(--clr-primary);
}

.timer span {
    color: var(--clr-secondary);
    margin-left: 0.25rem;
}

#streak-display {
    text-align: center;
    margin-top: 1rem;
    font-weight: 600;
    color: var(--clr-text-light);
    display: none; /* Controlled by JS */
}
#streak-display span#current-streak {
    font-weight: 700;
    color: var(--clr-accent);
    margin-left: 0.25rem;
}

/* Quiz Controls */
.quiz-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    padding-top: 1.5rem;
    border-top: 1px solid var(--clr-border);
}

.quiz-controls button {
    min-width: 120px;
}

/* Scoreboard */
#scoreboard {
    text-align: center;
    padding: 2rem;
    display: none; /* Controlled by JS */
    animation: fadeIn 1s both;
}

#scoreboard h3 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
}

#score {
    font-size: 1.5rem;
    color: var(--clr-text);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#final-streak {
    font-size: 2rem;
    color: var(--clr-accent);
    font-weight: 700;
    display: block;
    margin: 0.5rem 0;
}

#score-message {
    color: var(--clr-text-light);
    margin-bottom: 2rem;
    font-style: italic;
}

#scoreboard .btn {
    margin: 0.5rem;
}

/* ===========================
   Leaderboard Styles
   =========================== */
.leaderboard-controls {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 1.5rem;
    background: var(--clr-bg-light);
    border-radius: var(--radius);
    padding: 1.5rem;
    border: 1px solid var(--clr-border);
}

.leaderboard-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--clr-text);
}

.filter-group select {
    padding: 0.5rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    cursor: pointer;
    font-size: 0.9rem;
}

.leaderboard-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

#leaderboard-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    max-width: 600px; /* Slightly wider */
    margin-left: auto;
    margin-right: auto;
}

.leaderboard-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: 0.5rem;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--clr-border);
    transition: background-color var(--transition);
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.leaderboard-item:nth-child(odd) {
    background-color: rgba(128, 128, 128, 0.05);
}

.leaderboard-rank {
    font-weight: 700;
    min-width: 35px; /* Slightly wider */
    text-align: right;
    margin-right: 1rem;
    color: var(--clr-secondary);
}

.leaderboard-name {
    font-weight: 600;
    color: var(--clr-text);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.leaderboard-score {
    font-weight: 700;
    color: var(--clr-accent);
    text-align: right;
    font-size: 0.9rem;
    min-width: 100px;
}

.leaderboard-streak {
    font-weight: 500;
    color: var(--clr-secondary);
    text-align: right;
    font-size: 0.85rem;
    min-width: 80px;
}

.leaderboard-time {
    font-size: 0.8rem;
    color: var(--clr-text-light);
    text-align: right;
    min-width: 120px;
}

/* Perfect Score Highlighting */
.leaderboard-item.perfect-score {
    background: #fff3cd !important;
    border: 2px solid #ffc107 !important;
    border-radius: var(--radius);
    margin: 0.25rem 0;
}

.leaderboard-item.perfect-score .leaderboard-name {
    color: #856404 !important;
}

.leaderboard-item.perfect-score .leaderboard-rank {
    color: #856404 !important;
}

/* Rank Colors */
.leaderboard-item:nth-child(1) .leaderboard-rank { color: var(--clr-gold); }
.leaderboard-item:nth-child(2) .leaderboard-rank { color: var(--clr-silver); }
.leaderboard-item:nth-child(3) .leaderboard-rank { color: var(--clr-bronze); }

#clear-leaderboard {
    margin-top: 1rem; /* Add space above clear button */
}

/* ===========================
   Learn Section Styles
   =========================== */
.learn-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.learn-card {
    background: var(--clr-card);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition), box-shadow var(--transition);
    border: 1px solid var(--clr-border);
}

.learn-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.learn-card img {
    width: 100%;
    height: 180px;
    object-fit: cover; /* Changed from contain for better fill */
    background-color: #eee; /* Placeholder background */
    border-bottom: 1px solid var(--clr-border);
    border-radius: 0; /* Remove rounding from top of image */
}

.learn-card-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.learn-card h3 {
    color: var(--clr-secondary);
    margin-bottom: 0.5rem;
    margin-top: 0;
}

.learn-card p {
    color: var(--clr-text-light);
    flex-grow: 1;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.learn-card .btn {
    margin-top: auto; /* Push button to bottom */
    align-self: flex-start; /* Align button left */
}

/* Theme-specific learn card styles are in themes.css */

/* Learn Category Selector */
.learn-category-selector {
    margin-bottom: 2rem;
    text-align: center;
}

.learn-category-selector label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--clr-text);
}

.learn-category-dropdown {
    padding: 0.75rem 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    font-size: 1rem;
    min-width: 200px;
    cursor: pointer;
    transition: border-color var(--transition);
}

.learn-category-dropdown:focus {
    outline: none;
    border-color: var(--clr-secondary);
}

/* Learn Reference Grid */
.learn-reference-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.learn-reference-item {
    background: var(--clr-card);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    border: 1px solid var(--clr-border);
    transition: transform var(--transition), box-shadow var(--transition);
}

.learn-reference-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.learn-reference-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: var(--clr-bg);
}

.learn-reference-content {
    padding: 1.5rem;
}

.learn-reference-answer {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--clr-primary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    display: inline-block;
}

.learn-reference-answer::before {
    content: "✓";
    color: var(--clr-accent);
    font-weight: bold;
    font-size: 1.2rem;
}

.learn-reference-explanation {
    color: var(--clr-text-light);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.learn-reference-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.learn-reference-category {
    display: inline-block;
    background: var(--clr-secondary);
    color: var(--clr-button-text);
    padding: 0.25rem 0.75rem;
    border-radius: calc(var(--radius) / 2);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: capitalize;
}

.learn-reference-source {
    display: inline-block;
    background: var(--clr-accent);
    color: var(--clr-button-text);
    padding: 0.2rem 0.6rem;
    border-radius: calc(var(--radius) / 2);
    font-size: 0.75rem;
    font-weight: 400;
    opacity: 0.8;
    cursor: help;
}

/* No content message */
.no-content {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--clr-text-light);
    font-style: italic;
    padding: 2rem;
    background: var(--clr-card);
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
}

/* Learn Detail Styles */
#learn-detail { display: none; } /* Controlled by JS */

#learn-detail-content h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--clr-primary);
    border-bottom: 2px solid var(--clr-secondary);
    padding-bottom: 0.5rem;
}

#learn-detail-content h4 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--clr-secondary);
}

#learn-detail-content p {
    color: var(--clr-text-light);
    line-height: 1.7;
}

#learn-detail-content .learn-grid {
    margin-top: 1.5rem;
}

/* ===========================
   Settings Styles
   =========================== */
.settings-options {
    display: grid;
    gap: 1.5rem;
    max-width: 450px; /* Slightly wider */
    margin: 1rem auto;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 1rem;
    padding-top: 1rem;
}
.setting-item:first-child {
    padding-top: 0;
}
.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item label {
    font-weight: 600;
    color: var(--clr-primary);
    margin-right: 1rem;
}

.setting-item select {
    padding: 0.5rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    transition: border-color var(--transition), background-color var(--transition), color var(--transition);
    cursor: pointer;
    min-width: 130px;
}

/* Toggle switch styles are in components.css */

/* ===========================
   About Section Styles
   =========================== */
#about .learn-container { /* Reuse container style */
    text-align: center;
}

#about .learn-container div {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
    text-align: left;
}

#about strong {
    color: var(--clr-primary);
}

/* ===========================
   Responsive Styles
   =========================== */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.6rem; }
    h3 { font-size: 1.2rem; }

    /* Mobile Navigation Menu Styling */
    .nav-links {
        flex-direction: column;
        position: fixed;
        top: 0;
        right: 0; /* Start off-screen */
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background-color: var(--clr-header-bg);
        transform: translateX(100%);
        transition: transform 0.4s ease-in-out;
        z-index: 1050;
        padding-top: 5rem; /* Space for header/close button */
        box-shadow: -5px 0 15px rgba(0,0,0,0.2);
        overflow-y: auto;
        gap: 0;
    }

    .nav-links.show {
        transform: translateX(0);
    }

    .nav-links li {
        margin: 0;
        width: 100%;
    }

    .nav-links a {
        display: block;
        padding: 1rem 2rem;
        text-align: left;
        border-bottom: 1px solid var(--clr-secondary); /* Adjust color based on theme in themes.css */
        border-radius: 0;
        color: var(--clr-header-text);
        transform: none; /* Disable hover transform */
    }
    .nav-links a:hover,
    .nav-links a.active {
        background-color: var(--clr-secondary); /* Keep consistent highlight */
        transform: none;
    }

    .nav-links li:last-child a {
        border-bottom: none;
    }

    .burger {
        display: flex; /* Show burger on smaller screens */
    }

    /* Other adjustments */
    .hero {
        padding: 3rem 1rem;
        background-attachment: scroll; /* Disable parallax on mobile for better performance */
        min-height: 350px; /* Slightly smaller on mobile */
    }
    .hero h1 { font-size: 2rem; }
    .hero p { font-size: 1rem; }

    .features, .quiz-options, .learn-grid, .learn-reference-grid, .answer-options {
        grid-template-columns: 1fr; /* Stack grids on mobile */
    }

    .learn-reference-grid {
        gap: 1.5rem;
    }

    .learn-category-dropdown {
        min-width: 100%;
        max-width: 300px;
    }

    .learn-reference-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .quiz-controls {
        flex-direction: column;
        align-items: stretch;
    }
    .quiz-controls button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    .quiz-controls button:last-child {
        margin-bottom: 0;
    }

    .quiz-container,
    .learn-container,
    .settings-container,
    .leaderboard-container {
        padding: 1.5rem;
    }

    .leaderboard-filters {
        flex-direction: column;
    }

    .filter-group {
        min-width: 100%;
    }

    .leaderboard-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .leaderboard-item {
        grid-template-columns: auto 1fr auto;
        grid-template-rows: auto auto;
        padding: 0.75rem;
        gap: 0.25rem;
    }

    .leaderboard-rank {
        min-width: 30px;
        margin-right: 0.5rem;
        text-align: left;
        grid-row: 1;
        grid-column: 1;
    }

    .leaderboard-name {
        grid-row: 1;
        grid-column: 2;
        padding-right: 0.5rem;
    }

    .leaderboard-score {
        grid-row: 1;
        grid-column: 3;
        text-align: right;
        min-width: 80px;
    }

    .leaderboard-streak {
        grid-row: 2;
        grid-column: 2;
        font-size: 0.75rem;
        text-align: left;
    }

    .leaderboard-time {
        grid-row: 2;
        grid-column: 3;
        font-size: 0.7rem;
        text-align: right;
        min-width: 80px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
    }
    .setting-item label {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }

    .hero {
        min-height: 300px; /* Even smaller on very small screens */
        padding: 2.5rem 1rem;
    }
    .hero h1 {
        font-size: 1.8rem;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9), 0 0 12px rgba(0, 0, 0, 0.6); /* Enhanced shadow for small screens */
    }
    .hero p {
        font-size: 0.9rem;
        text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.9), 0 0 10px rgba(0, 0, 0, 0.5); /* Enhanced shadow for small screens */
    }

    nav { padding: 0 0.5rem; }
    header { padding: 0.75rem 0; }

    .logo { font-size: 1.1rem; }
    .logo svg { width: 2rem; height: 2rem; }

    .btn { padding: 0.6rem 1.2rem; font-size: 0.9rem; }

    .quiz-option,
    .answer-option {
        padding: 1rem;
    }

    .time-limit-selection label {
        display: block;
        margin-bottom: 0.5rem;
        margin-right: 0;
    }
    .time-limit-selection select {
        width: 100%;
    }

    .quiz-container,
    .learn-container,
    .settings-container,
    .leaderboard-container {
        padding: 1rem;
    }

    #player-name-area input[type="text"] {
        min-width: auto;
        width: calc(100% - 110px); /* Adjust based on button width */
    }

    .leaderboard-item {
         font-size: 0.9rem;
    }

    /* Player info responsive */
    .player-info {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    /* Modal responsive */
    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Admin responsive */
    .admin-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .admin-data-actions {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 0.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .options-container {
        grid-template-columns: 1fr;
    }

    .preview-options {
        grid-template-columns: 1fr;
    }

    /* Leaderboard stats responsive */
    .stats-row {
        grid-template-columns: 1fr;
    }

    .performer-item {
        grid-template-columns: auto 1fr;
        gap: 0.25rem;
    }

    .performer-item .score,
    .performer-item .accuracy,
    .performer-item .mode,
    .performer-item .category {
        display: none;
    }
}

/* ===========================
   Modal Styles
   =========================== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--clr-card);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--clr-border);
}

.modal-header h3 {
    margin: 0;
    color: var(--clr-text);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--clr-text-light);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background: var(--clr-bg-hover);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    margin-bottom: 1rem;
    color: var(--clr-text);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    background: var(--clr-input-bg);
    color: var(--clr-input-text);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: var(--clr-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.input-hint {
    font-size: 0.85rem;
    color: var(--clr-text-light);
    margin: 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--clr-border);
}

/* ===========================
   Admin Interface Styles
   =========================== */
.admin-container {
    background: var(--clr-card);
    border-radius: var(--radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    animation: fadeIn 1s ease-out both;
    margin-bottom: var(--section-margin-bottom);
    border: 1px solid var(--clr-border);
}

.admin-header {
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 1rem;
}

.admin-header h2 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
}

.admin-header p {
    color: var(--clr-text-light);
    margin-top: 0.5rem;
}

.admin-actions {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    margin-bottom: 2rem;
}

.admin-card {
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    padding: 1.5rem;
    text-align: center;
    transition: transform var(--transition), box-shadow var(--transition);
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.admin-card h3 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
}

.admin-card p {
    color: var(--clr-text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.admin-data-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.admin-stats {
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    padding: 1.5rem;
}

.admin-stats h3 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--clr-card);
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--clr-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--clr-text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===========================
   Admin Modal Form Styles
   =========================== */
.modal-large .modal-content {
    max-width: 700px;
    width: 95%;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--clr-text);
}

.input-hint {
    font-size: 0.85rem;
    color: var(--clr-text-secondary);
    margin-top: 0.5rem;
    font-style: italic;
    line-height: 1.4;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: background-color var(--transition);
}

.checkbox-label:hover {
    background-color: var(--clr-background);
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.options-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.option-input {
    margin-bottom: 0.5rem;
}

.image-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.image-upload-section,
.url-input-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.image-upload-section {
    margin-bottom: 2rem; /* Extra space for drag-drop hint */
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: var(--clr-primary);
    color: var(--clr-button-text);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 2px dashed var(--clr-border);
    min-width: 200px;
    justify-content: center;
    position: relative;
}

.file-upload-label:hover {
    background: var(--clr-primary-dark);
    border-color: var(--clr-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-upload-label.drag-over {
    background: var(--clr-accent);
    border-color: var(--clr-primary);
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.file-upload-label::after {
    content: 'oder hierher ziehen';
    position: absolute;
    bottom: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--clr-text-light);
    white-space: nowrap;
}

.file-input {
    display: none;
}

.upload-icon {
    font-size: 1.2rem;
}

.upload-text {
    font-size: 0.9rem;
}

.image-preview {
    margin-top: 1rem;
    text-align: center;
    padding: 1rem;
    background: var(--clr-background-light);
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
}

.image-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--clr-text-light);
}

.image-info span {
    padding: 0.25rem 0.5rem;
    background: var(--clr-background);
    border-radius: calc(var(--radius) / 2);
}

.question-preview {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
}

.question-preview h4 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
}

.preview-content {
    background: var(--clr-card);
    padding: 1rem;
    border-radius: var(--radius);
    border: 1px solid var(--clr-border);
}

.preview-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 1rem;
}

.preview-option {
    padding: 0.75rem;
    background: var(--clr-secondary);
    color: var(--clr-button-text);
    border-radius: var(--radius);
    text-align: center;
    font-size: 0.9rem;
}

.preview-option.correct {
    background: var(--clr-success);
    font-weight: 600;
}

.preview-image {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius);
    margin: 1rem 0;
}

/* ===========================
   Leaderboard Stats Modal Styles
   =========================== */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    padding: 1rem;
    text-align: center;
}

.stat-card h4 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--clr-text);
}

.top-performers {
    margin-bottom: 2rem;
}

.top-performers h4 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 0.5rem;
}

.performers-list {
    max-height: 300px;
    overflow-y: auto;
}

.performer-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto auto auto;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    margin-bottom: 0.5rem;
    align-items: center;
}

.performer-item .rank {
    font-weight: 600;
    color: var(--clr-primary);
    min-width: 30px;
}

.performer-item .name {
    font-weight: 600;
}

.performer-item .score,
.performer-item .accuracy,
.performer-item .mode,
.performer-item .category {
    font-size: 0.85rem;
    color: var(--clr-text-light);
}

.category-breakdown h4 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 0.5rem;
}

.mode-stats {
    margin-bottom: 1.5rem;
}

.mode-stats h5 {
    color: var(--clr-secondary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.category-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    margin-bottom: 0.25rem;
}

.category-name {
    font-weight: 500;
}

.category-count {
    font-size: 0.85rem;
    color: var(--clr-text-light);
    background: var(--clr-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-weight: 600;
}

/* ===========================
   Leaderboard Statistics Modal
   =========================== */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    padding: 1rem;
    text-align: center;
}

.stat-card h4 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--clr-text);
}

.top-performers, .category-breakdown {
    margin-bottom: 2rem;
}

.top-performers h4, .category-breakdown h4 {
    color: var(--clr-primary);
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--clr-border);
    padding-bottom: 0.5rem;
}

.performers-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
}

.performer-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--clr-border);
    background: var(--clr-card);
}

.performer-item:last-child {
    border-bottom: none;
}

.performer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.performer-name {
    font-weight: 600;
    color: var(--clr-text);
}

.performer-details {
    font-size: 0.85rem;
    color: var(--clr-text-light);
}

.performer-score {
    font-weight: 700;
    color: var(--clr-primary);
    text-align: right;
}

.category-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.category-stat-item {
    background: var(--clr-background);
    border: 1px solid var(--clr-border);
    border-radius: var(--radius);
    padding: 1rem;
}

.category-stat-item h5 {
    color: var(--clr-primary);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.category-entries {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.category-name {
    font-size: 0.9rem;
    color: var(--clr-text);
    text-transform: capitalize;
}

.category-count {
    font-weight: 600;
    color: var(--clr-secondary);
}